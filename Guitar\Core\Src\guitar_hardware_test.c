#include "guitar.h"
#include "OLED.h"
#include <stdio.h>

/**
 * @brief 硬件诊断测试 - 彻底排查音调问题
 */

/**
 * @brief 测试极端频率差异
 */
void Guitar_TestExtremeFrequencies(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Extreme Freq Test", OLED_8X16);
    OLED_Update();
    
    // 测试极端频率差异，如果这些都听不出区别，说明硬件有问题
    uint16_t testFreqs[] = {100, 500, 1000, 2000, 4000};
    const char* freqNames[] = {"100Hz", "500Hz", "1kHz", "2kHz", "4kHz"};
    
    for (uint8_t i = 0; i < 5; i++) {
        char buffer[32];
        OLED_Clear();
        OLED_ShowString(0, 0, "Playing:", OLED_8X16);
        OLED_ShowString(0, 16, freqNames[i], OLED_8X16);
        
        // 显示实际PWM参数
        uint32_t timerClock = 1000000; // 1MHz after prescaler
        uint32_t period = timerClock / testFreqs[i];
        
        sprintf(buffer, "Period: %lu", period);
        OLED_ShowString(0, 32, buffer, OLED_6X8);
        
        sprintf(buffer, "CCR: %lu", period/2);
        OLED_ShowString(0, 42, buffer, OLED_6X8);
        
        OLED_Update();
        
        // 直接设置PWM参数
        __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2);
        htim1.Instance->EGR = TIM_EGR_UG;
        
        HAL_Delay(3000); // 每个频率播放3秒
        
        // 停止
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);
        HAL_Delay(500);
    }
}

/**
 * @brief 测试PWM输出是否正常
 */
void Guitar_TestPWMOutput(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "PWM Output Test", OLED_8X16);
    OLED_Update();
    
    // 检查定时器是否启动
    if (!(htim1.Instance->CR1 & TIM_CR1_CEN)) {
        OLED_ShowString(0, 16, "TIM1 NOT STARTED!", OLED_6X8);
        OLED_Update();
        HAL_Delay(2000);
        
        // 尝试启动定时器
        HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
    }
    
    // 显示当前PWM状态
    char buffer[32];
    uint32_t arr = __HAL_TIM_GET_AUTORELOAD(&htim1);
    uint32_t ccr = __HAL_TIM_GET_COMPARE(&htim1, TIM_CHANNEL_1);
    
    sprintf(buffer, "ARR: %lu", arr);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    sprintf(buffer, "CCR: %lu", ccr);
    OLED_ShowString(0, 36, buffer, OLED_6X8);
    
    sprintf(buffer, "CR1: 0x%lX", htim1.Instance->CR1);
    OLED_ShowString(0, 46, buffer, OLED_6X8);
    
    sprintf(buffer, "CCER: 0x%lX", htim1.Instance->CCER);
    OLED_ShowString(0, 56, buffer, OLED_6X8);
    
    OLED_Update();
    HAL_Delay(3000);
}

/**
 * @brief 测试GPIO引脚输出
 */
void Guitar_TestGPIOOutput(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "GPIO Test", OLED_8X16);
    OLED_ShowString(0, 16, "PA8 Manual Toggle", OLED_6X8);
    OLED_Update();
    
    // 临时将PA8配置为普通GPIO输出
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_8;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    // 手动产生不同频率的方波
    uint16_t testFreqs[] = {200, 400, 800, 1600};
    
    for (uint8_t f = 0; f < 4; f++) {
        uint32_t halfPeriod = 500000 / testFreqs[f]; // 微秒
        
        char buffer[32];
        sprintf(buffer, "GPIO %dHz", testFreqs[f]);
        OLED_ShowString(0, 26, buffer, OLED_6X8);
        OLED_Update();
        
        // 产生方波 2秒
        for (uint16_t i = 0; i < testFreqs[f] * 2; i++) {
            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_SET);
            HAL_Delay(halfPeriod / 1000);
            HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_RESET);
            HAL_Delay(halfPeriod / 1000);
        }
        
        HAL_Delay(500);
    }
    
    // 恢复PA8为PWM功能
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Alternate = GPIO_AF1_TIM1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}

/**
 * @brief 检查蜂鸣器类型
 */
void Guitar_CheckBuzzerType(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Buzzer Type Test", OLED_8X16);
    OLED_Update();
    
    // 测试1：直流电压测试（检查是否为有源蜂鸣器）
    OLED_ShowString(0, 16, "Test 1: DC Voltage", OLED_6X8);
    OLED_Update();
    
    // 将PA8设为高电平
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPIO_PIN_8;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_SET);
    HAL_Delay(2000);
    
    OLED_ShowString(0, 26, "If sound: Active", OLED_6X8);
    OLED_ShowString(0, 36, "If no sound: Passive", OLED_6X8);
    OLED_Update();
    HAL_Delay(3000);
    
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_RESET);
    
    // 测试2：低频方波测试
    OLED_Clear();
    OLED_ShowString(0, 0, "Test 2: Low Freq", OLED_8X16);
    OLED_ShowString(0, 16, "10Hz Square Wave", OLED_6X8);
    OLED_Update();
    
    // 产生10Hz方波，应该能听到"嗒嗒"声
    for (uint16_t i = 0; i < 50; i++) {
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_SET);
        HAL_Delay(50); // 50ms
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, GPIO_PIN_RESET);
        HAL_Delay(50); // 50ms
    }
    
    OLED_ShowString(0, 26, "Should hear clicks", OLED_6X8);
    OLED_Update();
    HAL_Delay(2000);
    
    // 恢复PWM功能
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Alternate = GPIO_AF1_TIM1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}

/**
 * @brief 检查连接和电路
 */
void Guitar_CheckConnection(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Connection Check", OLED_8X16);
    OLED_Update();
    
    // 检查PA8引脚配置
    uint32_t moder = (GPIOA->CRH >> 0) & 0xF; // PA8在CRH的低4位
    
    char buffer[32];
    sprintf(buffer, "PA8 Config: 0x%lX", moder);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    
    // 应该是0xB (复用推挽输出，50MHz)
    if (moder == 0xB) {
        OLED_ShowString(0, 26, "PA8 Config: OK", OLED_6X8);
    } else {
        OLED_ShowString(0, 26, "PA8 Config: ERROR", OLED_6X8);
    }
    
    // 检查定时器时钟使能
    if (RCC->APB2ENR & RCC_APB2ENR_TIM1EN) {
        OLED_ShowString(0, 36, "TIM1 Clock: OK", OLED_6X8);
    } else {
        OLED_ShowString(0, 36, "TIM1 Clock: ERROR", OLED_6X8);
    }
    
    // 检查GPIOA时钟使能
    if (RCC->APB2ENR & RCC_APB2ENR_IOPAEN) {
        OLED_ShowString(0, 46, "GPIOA Clock: OK", OLED_6X8);
    } else {
        OLED_ShowString(0, 46, "GPIOA Clock: ERROR", OLED_6X8);
    }
    
    OLED_Update();
    HAL_Delay(5000);
}

/**
 * @brief 完整硬件诊断序列
 */
void Guitar_FullHardwareDiagnostic(void)
{
    // 1. 检查连接和配置
    Guitar_CheckConnection();
    
    // 2. 检查蜂鸣器类型
    Guitar_CheckBuzzerType();
    
    // 3. 测试PWM输出
    Guitar_TestPWMOutput();
    
    // 4. 测试GPIO手动输出
    Guitar_TestGPIOOutput();
    
    // 5. 测试极端频率差异
    Guitar_TestExtremeFrequencies();
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Diagnostic", OLED_8X16);
    OLED_ShowString(0, 16, "Complete!", OLED_8X16);
    OLED_Update();
}
