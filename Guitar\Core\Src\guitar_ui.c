#include "guitar_ui.h"
#include "guitar_effects.h"
#include "guitar_chords.h"
#include "guitar_rhythm.h"
#include "guitar_scales.h"
#include "guitar_tuner.h"
#include "guitar_games.h"
#include "OLED.h"
#include <string.h>
#include <stdio.h>

/* 全局UI状态 */
UIState_t uiState = {
    .currentMode = UI_MODE_MAIN,
    .selectedItem = 0,
    .menuDepth = 0,
    .brightness = 80,
    .autoSleep = 5,
    .needsUpdate = 1
};

Animation_t currentAnim = {0};

/* 主菜单项 */
const MenuItem_t mainMenu[] = {
    {"Play", 1, NULL},
    {"Chords", 2, NULL}, 
    {"Scales", 3, NULL},
    {"Tuner", 4, NULL},
    {"Games", 5, NULL},
    {"Settings", 6, NULL}
};

/**
 * @brief 初始化UI系统
 */
void Guitar_InitUI(void)
{
    uiState.currentMode = UI_MODE_MAIN;
    uiState.selectedItem = 0;
    uiState.lastActivity = HAL_GetTick();
    uiState.needsUpdate = 1;
    
    // 初始化各子系统
    Guitar_InitEffects();
    Guitar_InitRhythmSystem();
    Guitar_InitScaleSystem();
    Guitar_InitTuner();
    Guitar_InitGames();
    
    // 显示启动动画
    Guitar_StartAnimation(ANIM_FADE_IN, 1000);
}

/**
 * @brief 更新UI显示
 */
void Guitar_UpdateUI(void)
{
    if (!uiState.needsUpdate) return;
    
    // 清屏
    OLED_Clear();
    
    // 根据当前模式绘制界面
    switch (uiState.currentMode) {
        case UI_MODE_MAIN:
            Guitar_DrawMainScreen();
            break;
        case UI_MODE_CHORDS:
            Guitar_DrawChordsScreen();
            break;
        case UI_MODE_SCALES:
            Guitar_DrawScalesScreen();
            break;
        case UI_MODE_TUNER:
            Guitar_DrawTunerScreen();
            break;
        case UI_MODE_GAMES:
            Guitar_DrawGameScreen();
            break;
        case UI_MODE_SETTINGS:
            Guitar_DrawSettingsScreen();
            break;
        default:
            Guitar_DrawMainScreen();
            break;
    }
    
    // 更新动画
    Guitar_UpdateAnimation();
    
    // 更新显示
    OLED_Update();
    uiState.needsUpdate = 0;
}

/**
 * @brief 绘制主界面
 */
void Guitar_DrawMainScreen(void)
{
    // 标题
    OLED_ShowString(0, 0, "STM32 Guitar Pro", OLED_8X16);
    
    // 当前状态
    char statusLine[32];
    sprintf(statusLine, "Mode:%s Oct:%d Vol:%d", 
            guitarStatus.mode == GUITAR_MODE_SINGLE ? "Play" :
            guitarStatus.mode == GUITAR_MODE_CHORD ? "Chord" : "Scale",
            guitarStatus.octave, guitarStatus.volume);
    OLED_ShowString(0, 16, statusLine, OLED_6X8);
    
    // 当前音符
    if (guitarStatus.currentNote > 0) {
        sprintf(statusLine, "Playing: %dHz", guitarStatus.currentNote);
        OLED_ShowString(0, 26, statusLine, OLED_6X8);
    } else {
        OLED_ShowString(0, 26, "Ready to play...", OLED_6X8);
    }
    
    // 按键状态指示器
    Guitar_DrawKeyboard(0); // 传入当前按键状态
    
    // 菜单提示
    OLED_ShowString(0, 50, "Hold Key1: Menu", OLED_6X8);
}

/**
 * @brief 绘制和弦界面
 */
void Guitar_DrawChordsScreen(void)
{
    OLED_ShowString(0, 0, "Chord Mode", OLED_8X16);
    
    // 显示当前选中的和弦
    // TODO: 实现和弦显示逻辑
    OLED_ShowString(0, 16, "C Major", OLED_6X8);
    OLED_ShowString(0, 26, "Press keys to play", OLED_6X8);
    
    // 和弦图示
    OLED_ShowString(0, 40, "C-E-G", OLED_6X8);
}

/**
 * @brief 绘制调音器界面
 */
void Guitar_DrawTunerScreen(void)
{
    OLED_ShowString(0, 0, "Tuner", OLED_8X16);
    
    if (tunerState.isActive) {
        // 显示检测到的音符和频率
        char tunerInfo[32];
        sprintf(tunerInfo, "Note: %s", lastDetection.noteName);
        OLED_ShowString(0, 16, tunerInfo, OLED_6X8);
        
        sprintf(tunerInfo, "Freq: %dHz", lastDetection.frequency);
        OLED_ShowString(0, 26, tunerInfo, OLED_6X8);
        
        // 调音指示器
        Guitar_DrawTunerNeedle(lastDetection.centsDiff);
        
        // 准确度指示
        if (abs(lastDetection.centsDiff) < 10) {
            OLED_ShowString(0, 50, "IN TUNE!", OLED_6X8);
        } else if (lastDetection.centsDiff < 0) {
            OLED_ShowString(0, 50, "Too Low", OLED_6X8);
        } else {
            OLED_ShowString(0, 50, "Too High", OLED_6X8);
        }
    } else {
        OLED_ShowString(0, 20, "Press Key1 to start", OLED_6X8);
    }
}

/**
 * @brief 处理按键输入
 * @param buttonIndex 按键索引
 * @param longPress 是否长按
 */
void Guitar_HandleButtonPress(uint8_t buttonIndex, uint8_t longPress)
{
    uiState.lastActivity = HAL_GetTick();
    uiState.needsUpdate = 1;
    
    switch (uiState.currentMode) {
        case UI_MODE_MAIN:
            if (longPress && buttonIndex == 0) {
                // Key1长按进入菜单
                Guitar_ChangeMode(UI_MODE_CHORDS);
            } else if (longPress && buttonIndex == 1) {
                // Key2长按进入调音器
                Guitar_ChangeMode(UI_MODE_TUNER);
            } else if (longPress && buttonIndex == 2) {
                // Key3长按进入游戏
                Guitar_ChangeMode(UI_MODE_GAMES);
            }
            break;
            
        case UI_MODE_TUNER:
            if (buttonIndex == 0) {
                if (tunerState.isActive) {
                    Guitar_StopTuner();
                } else {
                    Guitar_StartTuner(TUNE_MODE_CHROMATIC);
                }
            } else if (longPress) {
                Guitar_ChangeMode(UI_MODE_MAIN);
            }
            break;
            
        case UI_MODE_GAMES:
            if (!gameState.isActive) {
                // 选择游戏
                if (buttonIndex == 0) {
                    Guitar_StartGame(GAME_SIMON);
                } else if (buttonIndex == 1) {
                    Guitar_StartGame(GAME_NOTE_GUESS);
                }
            } else {
                // 游戏中的输入处理
                Guitar_ProcessGameInput(buttonIndex);
            }
            
            if (longPress) {
                Guitar_StopGame();
                Guitar_ChangeMode(UI_MODE_MAIN);
            }
            break;
            
        default:
            if (longPress) {
                Guitar_ChangeMode(UI_MODE_MAIN);
            }
            break;
    }
}

/**
 * @brief 绘制键盘指示器
 * @param pressedKeys 按下的按键位掩码
 */
void Guitar_DrawKeyboard(uint8_t pressedKeys)
{
    const char* noteLabels[] = {"C", "D", "E", "F", "G", "A"};
    
    for (int i = 0; i < 6; i++) {
        uint8_t x = 10 + i * 16;
        uint8_t y = 35;
        
        // 绘制按键框
        if (guitarStatus.keyPressed[i]) {
            OLED_ShowString(x, y, "*", OLED_6X8);  // 按下状态
        } else {
            OLED_ShowString(x, y, "o", OLED_6X8);  // 释放状态
        }
        
        // 音符标签
        OLED_ShowString(x, y + 8, noteLabels[i], OLED_6X8);
    }
}

/**
 * @brief 启动动画效果
 * @param type 动画类型
 * @param duration 持续时间
 */
void Guitar_StartAnimation(uint8_t type, uint16_t duration)
{
    currentAnim.isActive = 1;
    currentAnim.type = type;
    currentAnim.progress = 0;
    currentAnim.startTime = HAL_GetTick();
    currentAnim.duration = duration;
}

/**
 * @brief 更新动画
 */
void Guitar_UpdateAnimation(void)
{
    if (!currentAnim.isActive) return;
    
    uint32_t elapsed = HAL_GetTick() - currentAnim.startTime;
    
    if (elapsed >= currentAnim.duration) {
        currentAnim.isActive = 0;
        currentAnim.progress = 100;
    } else {
        currentAnim.progress = (elapsed * 100) / currentAnim.duration;
    }
    
    // 根据动画类型应用效果
    switch (currentAnim.type) {
        case ANIM_FADE_IN:
            // 淡入效果 - 可以通过调整对比度实现
            break;
        case ANIM_SLIDE_LEFT:
            // 左滑效果 - 可以通过坐标偏移实现
            break;
        default:
            break;
    }
}

/**
 * @brief 改变UI模式
 * @param newMode 新模式
 */
void Guitar_ChangeMode(UIMode_t newMode)
{
    uiState.currentMode = newMode;
    uiState.selectedItem = 0;
    uiState.needsUpdate = 1;
    
    // 模式切换时的初始化
    switch (newMode) {
        case UI_MODE_TUNER:
            // 自动启动调音器
            break;
        case UI_MODE_GAMES:
            // 重置游戏状态
            break;
        default:
            break;
    }
    
    Guitar_StartAnimation(ANIM_SLIDE_LEFT, 300);
}
