#ifndef __GUITAR_CHORDS_H
#define __GUITAR_CHORDS_H

#include "guitar.h"

/* 和弦类型 */
typedef enum {
    CHORD_MAJOR = 0,      // 大和弦
    CHORD_MINOR,          // 小和弦
    CHORD_SEVENTH,        // 七和弦
    CHORD_DIMINISHED,     // 减和弦
    CHORD_AUGMENTED,      // 增和弦
    CHORD_SUSPENDED      // 挂和弦
} ChordType_t;

/* 智能和弦结构体 */
typedef struct {
    char name[12];        // 和弦名称
    uint16_t notes[6];    // 6个音符对应6个按键
    uint8_t noteCount;    // 实际音符数量
    ChordType_t type;     // 和弦类型
    uint8_t difficulty;   // 难度等级 (1-5)
} SmartChord_t;

/* 和弦进行 */
typedef struct {
    char name[16];        // 进行名称
    uint8_t chords[8];    // 和弦索引序列
    uint8_t chordCount;   // 和弦数量
    uint16_t tempo;       // 节拍 (BPM)
} ChordProgression_t;

/* 和弦识别系统 */
typedef struct {
    uint8_t pressedKeys;  // 当前按下的按键位掩码
    uint8_t detectedChord; // 识别的和弦
    uint8_t confidence;   // 识别置信度
} ChordRecognition_t;

/* 全局变量 */
extern const SmartChord_t smartChords[];
extern const uint8_t smartChordCount;
extern const ChordProgression_t chordProgressions[];
extern const uint8_t progressionCount;
extern ChordRecognition_t chordRecog;

/* 函数声明 */
void Guitar_InitChordSystem(void);
void Guitar_PlaySmartChord(uint8_t chordIndex, uint8_t playStyle);
uint8_t Guitar_RecognizeChord(uint8_t keyMask);
void Guitar_PlayProgression(uint8_t progressionIndex);
void Guitar_GenerateChordFromScale(uint8_t rootNote, ChordType_t type, SmartChord_t* output);
void Guitar_TeachChord(uint8_t chordIndex); // 教学模式
uint8_t Guitar_SuggestNextChord(uint8_t currentChord); // 智能建议

/* 演奏风格 */
#define PLAY_STYLE_BLOCK     0  // 和音块
#define PLAY_STYLE_ARPEGGIO  1  // 琶音
#define PLAY_STYLE_STRUM     2  // 扫弦

#endif /* __GUITAR_CHORDS_H */
