#ifndef __GUITAR_TUNING_TEST_H
#define __GUITAR_TUNING_TEST_H

#include "guitar.h"

/**
 * @brief 电子吉他音调准确性测试模块
 * 
 * 这个模块提供了一系列测试函数来验证电子吉他的音调准确性，
 * 包括频率精度测试、音程准确性测试等功能。
 */

/* 测试函数声明 */

/**
 * @brief 执行完整的音调准确性测试
 * 测试所有6个音符的频率精度，计算误差统计
 */
void Guitar_RunTuningAccuracyTest(void);

/**
 * @brief 播放音调测试序列
 * 依次播放所有音符，每个音符持续2秒，用于听觉验证
 */
void Guitar_PlayTuningTestSequence(void);

/**
 * @brief 比较修复前后的频率精度
 * 对比单精度和双精度浮点数计算的频率精度差异
 */
void Guitar_CompareFrequencyAccuracy(void);

/**
 * @brief 测试音程准确性
 * 测试完全五度和大三度等音程的频率比例准确性
 */
void Guitar_TestIntervals(void);

/**
 * @brief 快速音调检查
 * 快速检查A4音符的准确性，用于日常校准
 */
static inline void Guitar_QuickTuningCheck(void)
{
    Guitar_PlayTuningA4();
    HAL_Delay(1000);
    Guitar_StopNote();
}

/* 测试常量定义 */
#define TUNING_TEST_DURATION_MS     2000    // 每个音符的测试持续时间
#define TUNING_TEST_INTERVAL_MS     500     // 音符间隔时间
#define TUNING_ACCURACY_THRESHOLD   0.1f    // 可接受的频率误差阈值 (%)
#define TUNING_EXCELLENT_THRESHOLD  0.01f   // 优秀的频率误差阈值 (%)

/* 音程比例常量 (基于十二平均律) */
#define RATIO_PERFECT_FIFTH     1.498307f   // 完全五度 (2^(7/12))
#define RATIO_MAJOR_THIRD       1.259921f   // 大三度 (2^(4/12))
#define RATIO_PERFECT_FOURTH    1.334840f   // 完全四度 (2^(5/12))
#define RATIO_MINOR_THIRD       1.189207f   // 小三度 (2^(3/12))
#define RATIO_OCTAVE            2.000000f   // 八度 (2^(12/12))

#endif /* __GUITAR_TUNING_TEST_H */
