#include "guitar.h"
#include "guitar_tuning_test.h"
#include "OLED.h"
#include <stdio.h>
#include <math.h>

/**
 * @brief 音调准确性测试套件
 * 这个文件包含了用于验证电子吉他音调准确性的测试函数
 */

/* 测试结果结构体 */
typedef struct {
    float targetFreq;
    float actualFreq;
    float errorHz;
    float errorPercent;
    uint32_t period;
} TuningTestResult_t;

/**
 * @brief 执行完整的音调准确性测试
 */
void Guitar_RunTuningAccuracyTest(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Tuning Test", OLED_8X16);
    OLED_ShowString(0, 16, "Starting...", OLED_6X8);
    OLED_Update();
    
    // 获取定时器时钟频率
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }
    
    TuningTestResult_t results[6];
    float maxError = 0.0f;
    float avgError = 0.0f;
    
    // 测试所有6个音符
    for (uint8_t i = 0; i < 6; i++) {
        results[i].targetFreq = Guitar_GetPreciseNoteFrequency(i);
        
        // 计算实际PWM周期和频率
        double precisePeriod = (double)timerClock / (double)results[i].targetFreq;
        results[i].period = (uint32_t)(precisePeriod + 0.5);
        results[i].actualFreq = (float)((double)timerClock / (double)results[i].period);
        
        // 计算误差
        results[i].errorHz = results[i].actualFreq - results[i].targetFreq;
        results[i].errorPercent = (results[i].errorHz / results[i].targetFreq) * 100.0f;
        
        // 统计最大误差
        float absError = fabsf(results[i].errorPercent);
        if (absError > maxError) {
            maxError = absError;
        }
        avgError += absError;
    }
    
    avgError /= 6.0f;
    
    // 显示测试结果
    char buffer[32];
    OLED_Clear();
    OLED_ShowString(0, 0, "Test Results", OLED_8X16);
    
    sprintf(buffer, "Max Err: %.4f%%", maxError);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    
    sprintf(buffer, "Avg Err: %.4f%%", avgError);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    // 判断测试结果
    if (maxError < 0.01f) {
        OLED_ShowString(0, 36, "EXCELLENT", OLED_6X8);
    } else if (maxError < 0.1f) {
        OLED_ShowString(0, 36, "GOOD", OLED_6X8);
    } else if (maxError < 1.0f) {
        OLED_ShowString(0, 36, "ACCEPTABLE", OLED_6X8);
    } else {
        OLED_ShowString(0, 36, "POOR", OLED_6X8);
    }
    
    sprintf(buffer, "Clock: %luHz", timerClock);
    OLED_ShowString(0, 46, buffer, OLED_6X8);
    
    OLED_Update();
}

/**
 * @brief 播放音调测试序列
 */
void Guitar_PlayTuningTestSequence(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Playing Test", OLED_8X16);
    OLED_ShowString(0, 16, "Sequence...", OLED_6X8);
    OLED_Update();
    
    // 播放每个音符2秒，显示频率信息
    for (uint8_t i = 0; i < 6; i++) {
        float freq = Guitar_GetPreciseNoteFrequency(i);
        char noteNames[6][4] = {"C4", "D4", "E4", "F4", "G4", "A4"};
        char buffer[32];
        
        // 显示当前播放的音符
        OLED_Clear();
        OLED_ShowString(0, 0, "Playing:", OLED_8X16);
        OLED_ShowString(0, 16, noteNames[i], OLED_8X16);
        
        sprintf(buffer, "%.2f Hz", freq);
        OLED_ShowString(0, 32, buffer, OLED_6X8);
        OLED_Update();
        
        // 播放音符
        Guitar_SetPreciseFrequency(freq);
        HAL_Delay(2000);
        Guitar_StopNote();
        HAL_Delay(500);
    }
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Test Complete", OLED_8X16);
    OLED_Update();
}

/**
 * @brief 比较修复前后的频率精度
 */
void Guitar_CompareFrequencyAccuracy(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Accuracy Test", OLED_8X16);
    OLED_Update();
    
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }
    
    // 测试A4音符 (440Hz) 的精度
    float targetA4 = 440.0f;
    
    // 旧方法 (单精度浮点数)
    uint32_t oldPeriod = (uint32_t)((float)timerClock / targetA4 + 0.5f);
    float oldActualFreq = (float)timerClock / (float)oldPeriod;
    float oldError = ((oldActualFreq - targetA4) / targetA4) * 100.0f;
    
    // 新方法 (双精度浮点数)
    double precisePeriod = (double)timerClock / (double)targetA4;
    uint32_t newPeriod = (uint32_t)(precisePeriod + 0.5);
    double newActualFreq = (double)timerClock / (double)newPeriod;
    float newError = ((float)(newActualFreq - targetA4) / targetA4) * 100.0f;
    
    // 显示比较结果
    char buffer[32];
    OLED_Clear();
    OLED_ShowString(0, 0, "A4 Accuracy", OLED_8X16);
    
    sprintf(buffer, "Old: %.6f%%", oldError);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    
    sprintf(buffer, "New: %.6f%%", newError);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    float improvement = fabsf(oldError) - fabsf(newError);
    sprintf(buffer, "Impr: %.6f%%", improvement);
    OLED_ShowString(0, 36, buffer, OLED_6X8);
    
    if (improvement > 0) {
        OLED_ShowString(0, 46, "IMPROVED!", OLED_6X8);
    } else {
        OLED_ShowString(0, 46, "No change", OLED_6X8);
    }
    
    OLED_Update();
}

/**
 * @brief 测试音程准确性
 */
void Guitar_TestIntervals(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Interval Test", OLED_8X16);
    OLED_Update();
    
    // 测试完全五度 (C4 到 G4)
    float c4_freq = Guitar_GetPreciseNoteFrequency(0); // C4
    float g4_freq = Guitar_GetPreciseNoteFrequency(4); // G4
    double ratio = (double)g4_freq / (double)c4_freq;
    double expectedRatio = 1.498307; // 精确的完全五度比例 (2^(7/12))
    double ratioError = ((ratio - expectedRatio) / expectedRatio) * 100.0;
    
    char buffer[32];
    OLED_Clear();
    OLED_ShowString(0, 0, "Perfect 5th", OLED_8X16);
    OLED_ShowString(0, 16, "C4 -> G4", OLED_6X8);
    
    sprintf(buffer, "Ratio: %.6f", ratio);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    sprintf(buffer, "Error: %.4f%%", ratioError);
    OLED_ShowString(0, 36, buffer, OLED_6X8);
    
    if (fabsf(ratioError) < 0.01) {
        OLED_ShowString(0, 46, "PERFECT", OLED_6X8);
    } else if (fabsf(ratioError) < 0.1) {
        OLED_ShowString(0, 46, "GOOD", OLED_6X8);
    } else {
        OLED_ShowString(0, 46, "POOR", OLED_6X8);
    }
    
    OLED_Update();
    HAL_Delay(3000);
    
    // 测试大三度 (C4 到 E4)
    float e4_freq = Guitar_GetPreciseNoteFrequency(2); // E4
    double majorThirdRatio = (double)e4_freq / (double)c4_freq;
    double expectedMajorThird = 1.259921; // 精确的大三度比例 (2^(4/12))
    double majorThirdError = ((majorThirdRatio - expectedMajorThird) / expectedMajorThird) * 100.0;
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Major 3rd", OLED_8X16);
    OLED_ShowString(0, 16, "C4 -> E4", OLED_6X8);
    
    sprintf(buffer, "Ratio: %.6f", majorThirdRatio);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    sprintf(buffer, "Error: %.4f%%", majorThirdError);
    OLED_ShowString(0, 36, buffer, OLED_6X8);
    
    if (fabsf(majorThirdError) < 0.01) {
        OLED_ShowString(0, 46, "PERFECT", OLED_6X8);
    } else if (fabsf(majorThirdError) < 0.1) {
        OLED_ShowString(0, 46, "GOOD", OLED_6X8);
    } else {
        OLED_ShowString(0, 46, "POOR", OLED_6X8);
    }
    
    OLED_Update();
}
