#include "guitar.h"
#include "gpio.h"
#include "OLED.h"
#include <string.h>
#include <stdio.h>

/* 全局变量 */
GuitarStatus_t guitarStatus = {
    .mode = GUITAR_MODE_SINGLE,
    .octave = 4,
    .volume = 80,
    .keyPressed = {0},
    .currentNote = 0,
    .noteDuration = 0
};

/* 音符映射表 - 将6个按键映射到不同音符 */
const uint16_t noteFrequencies[6] = {
    NOTE_C4,    // KEY1 - Do
    NOTE_D4,    // KEY2 - Re
    NOTE_E4,    // KEY3 - Mi
    NOTE_F4,    // KEY4 - Fa
    NOTE_G4,    // KEY5 - So
    NOTE_A4     // KEY6 - La
};

/* 按键防抖相关变量 */
static uint8_t keyLastState[6] = {1, 1, 1, 1, 1, 1}; // 上次按键状态 (1=释放, 0=按下)
static uint32_t keyDebounceTime[6] = {0};             // 按键防抖计时
static uint8_t keyStableState[6] = {1, 1, 1, 1, 1, 1}; // 稳定的按键状态
static uint32_t keyPressTime[6] = {0};                // 按键按下时间
static uint8_t keyLongPressed[6] = {0};               // 长按标志

#define KEY_DEBOUNCE_DELAY 20  // 防抖延时 (ms)
#define KEY_LONG_PRESS_TIME 1000 // 长按时间 (ms)

/**
 * @brief 吉他系统初始化
 */
void Guitar_Init(void)
{
    // 初始化吉他状态
    guitarStatus.mode = GUITAR_MODE_SINGLE;
    guitarStatus.octave = 4;
    guitarStatus.volume = 80;
    guitarStatus.currentNote = 0;
    guitarStatus.noteDuration = 0;
    memset(guitarStatus.keyPressed, 0, sizeof(guitarStatus.keyPressed));
    
    // 启动PWM输出 (但不发声)
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
    Guitar_StopNote();
}

/**
 * @brief 设置PWM频率产生音调
 * @param frequency 频率值 (Hz)
 */
void Guitar_SetFrequency(uint16_t frequency)
{
    if (frequency == 0) {
        Guitar_StopNote();
        return;
    }
    
    // 计算PWM周期值
    // PWM时钟频率 = 72MHz / (71+1) = 1MHz
    uint32_t period = 1000000 / frequency;
    
    if (period > 65535) period = 65535; // 限制最大值
    if (period < 100) period = 100;     // 限制最小值
    
    // 设置新的周期和占空比
    __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2); // 50%占空比
    
    guitarStatus.currentNote = frequency;
}

/**
 * @brief 设置音量 (通过调整占空比实现)
 * @param volume 音量值 0-100
 */
void Guitar_SetVolume(uint8_t volume)
{
    if (volume > 100) volume = 100;
    
    guitarStatus.volume = volume;
    
    // 如果当前有音符在播放，更新占空比
    if (guitarStatus.currentNote > 0) {
        uint32_t period = __HAL_TIM_GET_AUTORELOAD(&htim1) + 1;
        uint32_t pulse = (period * volume) / 200; // volume/100 * 50%占空比
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, pulse);
    }
}

/**
 * @brief 播放指定频率和时长的音符
 * @param frequency 频率 (Hz)  
 * @param duration 持续时间 (ms)
 */
void Guitar_PlayNote(uint16_t frequency, uint16_t duration)
{
    Guitar_SetFrequency(frequency);
    Guitar_SetVolume(guitarStatus.volume);
    guitarStatus.noteDuration = HAL_GetTick() + duration;
}

/**
 * @brief 停止音符播放
 */
void Guitar_StopNote(void)
{
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0); // 占空比设为0
    guitarStatus.currentNote = 0;
    guitarStatus.noteDuration = 0;
}

/**
 * @brief 根据按键索引获取对应的音符频率
 * @param keyIndex 按键索引 (0-5)
 * @return 音符频率
 */
uint16_t Guitar_GetNoteFrequency(uint8_t keyIndex)
{
    if (keyIndex >= 6) return 0;
    
    // 根据八度调整频率
    uint16_t baseFreq = noteFrequencies[keyIndex];
    
    switch (guitarStatus.octave) {
        case 3: return baseFreq / 2;      // 低八度
        case 4: return baseFreq;          // 标准八度
        case 5: return baseFreq * 2;      // 高八度
        case 6: return baseFreq * 4;      // 超高八度
        default: return baseFreq;
    }
}

/**
 * @brief 检查指定按键是否被按下
 * @param keyIndex 按键索引 (0-5)
 * @return 1-按下, 0-释放
 */
uint8_t Guitar_IsKeyPressed(uint8_t keyIndex)
{
    GPIO_TypeDef* gpioPort = GPIOA;
    uint16_t gpioPin;
    
    switch (keyIndex) {
        case 0: gpioPin = KEY1_Pin; break;
        case 1: gpioPin = KEY2_Pin; break;
        case 2: gpioPin = KEY3_Pin; break;
        case 3: gpioPin = KEY4_Pin; break;
        case 4: gpioPin = KEY5_Pin; break;
        case 5: gpioPin = KEY6_Pin; break;
        default: return 0;
    }
    
    // 读取GPIO状态 (按下为低电平)
    return (HAL_GPIO_ReadPin(gpioPort, gpioPin) == GPIO_PIN_RESET) ? 1 : 0;
}

/**
 * @brief 扫描所有按键状态并处理防抖
 */
void Guitar_ScanKeys(void)
{
    uint32_t currentTime = HAL_GetTick();
    
    for (uint8_t i = 0; i < 6; i++) {
        uint8_t currentState = Guitar_IsKeyPressed(i);
        
        // 检测状态变化
        if (currentState != keyLastState[i]) {
            keyDebounceTime[i] = currentTime;
            keyLastState[i] = currentState;
        }
        
        // 防抖处理
        if ((currentTime - keyDebounceTime[i]) >= KEY_DEBOUNCE_DELAY) {
            if (currentState != keyStableState[i]) {
                keyStableState[i] = currentState;
                
                // 生成按键事件
                KeyEvent_t keyEvent;
                keyEvent.keyIndex = i;
                keyEvent.eventType = currentState ? KEY_EVENT_PRESS : KEY_EVENT_RELEASE;
                keyEvent.timestamp = currentTime;
                
                // 发送到按键事件队列
                osMessageQueuePut(KeyEventQueueHandle, &keyEvent, 0, 0);
                
                // 更新吉他状态
                guitarStatus.keyPressed[i] = currentState;
            }
        }
    }
}

/**
 * @brief 处理按键事件
 * @param keyEvent 按键事件指针
 */
void Guitar_ProcessKeyEvent(KeyEvent_t* keyEvent)
{
    if (!keyEvent || keyEvent->keyIndex >= 6) return;
    
    AudioCommand_t audioCmd;
    
    switch (keyEvent->eventType) {
        case KEY_EVENT_PRESS:
            // 按键按下 - 播放对应音符
            audioCmd.frequency = Guitar_GetNoteFrequency(keyEvent->keyIndex);
            audioCmd.duration = 0; // 持续播放直到释放
            audioCmd.volume = guitarStatus.volume;
            
            osMessageQueuePut(AudioCommandQueueHandle, &audioCmd, 0, 0);
            break;
            
        case KEY_EVENT_RELEASE:
            // 按键释放 - 停止播放
            audioCmd.frequency = 0;
            audioCmd.duration = 0;
            audioCmd.volume = 0;
            
            osMessageQueuePut(AudioCommandQueueHandle, &audioCmd, 0, 0);
            break;
            
        case KEY_EVENT_HOLD:
            // 长按处理 (暂时不实现)
            break;
    }
}

/**
 * @brief 测试OLED显示
 */
void Guitar_TestOLED(void)
{
    // 清屏
    OLED_Clear();
    
    // 测试不同位置的文字显示
    OLED_ShowString(0, 0, "OLED TEST", OLED_8X16);
    OLED_ShowString(0, 20, "Line 2", OLED_6X8);
    OLED_ShowString(0, 30, "Line 3", OLED_6X8);
    OLED_ShowString(0, 40, "Line 4", OLED_6X8);
    OLED_ShowString(0, 50, "Line 5", OLED_6X8);
    
    // 显示一些数字
    OLED_ShowNum(80, 20, 123, 3, OLED_6X8);
    OLED_ShowNum(80, 30, 456, 3, OLED_6X8);
    
    // 更新显示
    OLED_Update();
}

/**
 * @brief 更新OLED显示内容 (增强版)
 */
void Guitar_UpdateDisplay(void)
{
    char displayBuffer[32];
    
    // 清屏
    OLED_Clear();
    
    // === 第一行：标题和状态指示 ===
    OLED_ShowString(0, 0, "STM32 Guitar v2.0", OLED_6X8);
    
    // === 第二行：当前模式和八度 ===
    switch (guitarStatus.mode) {
        case GUITAR_MODE_SINGLE:
            sprintf(displayBuffer, "Mode:Single Oct:%d", guitarStatus.octave);
            break;
        case GUITAR_MODE_CHORD:
            sprintf(displayBuffer, "Mode:Chord  Oct:%d", guitarStatus.octave);
            break;
        case GUITAR_MODE_SCALE:
            sprintf(displayBuffer, "Mode:Scale  Oct:%d", guitarStatus.octave);
            break;
    }
    OLED_ShowString(0, 10, displayBuffer, OLED_6X8);
    
    // === 第三行：音量和当前音符 ===
    if (guitarStatus.currentNote > 0) {
        sprintf(displayBuffer, "Vol:%d%% Note:%dHz", guitarStatus.volume, guitarStatus.currentNote);
    } else {
        sprintf(displayBuffer, "Vol:%d%% Note:----", guitarStatus.volume);
    }
    OLED_ShowString(0, 20, displayBuffer, OLED_6X8);
    
    // === 第四行：按键状态指示 ===
    OLED_ShowString(0, 30, "Keys:", OLED_6X8);
    for (int i = 0; i < 6; i++) {
        if (guitarStatus.keyPressed[i]) {
            OLED_ShowString(30 + i * 8, 30, "*", OLED_6X8);  // 显示*表示按键按下
        } else {
            OLED_ShowString(30 + i * 8, 30, "o", OLED_6X8);  // 显示o表示按键释放
        }
    }
    
    // === 第五行：功能状态 ===
    strcpy(displayBuffer, "");
    if (recorder.isRecording) {
        strcat(displayBuffer, "REC ");
    }
    if (recorder.isPlaying) {
        strcat(displayBuffer, "PLAY ");
    }
    if (metronome.isRunning) {
        char bpmStr[16];
        sprintf(bpmStr, "BPM:%d ", metronome.bpm);
        strcat(displayBuffer, bpmStr);
    }
    
    if (strlen(displayBuffer) > 0) {
        OLED_ShowString(0, 40, displayBuffer, OLED_6X8);
    } else {
        OLED_ShowString(0, 40, "Ready to Rock!", OLED_6X8);
    }
    
    // === 第六行：音符名称显示 ===
    const char* noteNames[] = {"C", "D", "E", "F", "G", "A"};
    strcpy(displayBuffer, "Notes: ");
    for (int i = 0; i < 6; i++) {
        strcat(displayBuffer, noteNames[i]);
        if (i < 5) strcat(displayBuffer, " ");
    }
    OLED_ShowString(0, 50, displayBuffer, OLED_6X8);
    
    // 更新显示
    OLED_Update();
}
