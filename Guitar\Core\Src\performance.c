#include "performance.h"
#include "FreeRTOS.h"
#include "task.h"
#include "tim.h"
#include <string.h>

/* 全局变量 */
PerformanceStats_t perfStats = {0};
static volatile uint32_t perfTimerStart = 0;

/**
 * @brief 性能监控初始化
 */
void Performance_Init(void)
{
    memset(&perfStats, 0, sizeof(PerformanceStats_t));
}

/**
 * @brief 启动性能计时器
 */
void Performance_StartTimer(void)
{
    perfTimerStart = __HAL_TIM_GET_COUNTER(&htim1); // 使用现有定时器
}

/**
 * @brief 获取经过的时间 (微秒)
 * @return 经过的时间 (us)
 */
uint32_t Performance_GetElapsedTime(void)
{
    uint32_t current = __HAL_TIM_GET_COUNTER(&htim1);
    uint32_t elapsed;
    
    if (current >= perfTimerStart) {
        elapsed = current - perfTimerStart;
    } else {
        // 定时器溢出
        elapsed = (0xFFFFFFFF - perfTimerStart) + current;
    }
    
    // 转换为微秒 (假设定时器频率为1MHz)
    return elapsed;
}

/**
 * @brief 更新性能统计
 */
void Performance_UpdateStats(void)
{
    // 获取堆使用情况
    perfStats.freeHeapSize = xPortGetFreeHeapSize();
    
    // 获取任务栈使用情况
    TaskHandle_t tasks[3];
    tasks[0] = xTaskGetHandle("defaultTask");  // KeyScanTask
    tasks[1] = xTaskGetHandle("AudioTask");
    tasks[2] = xTaskGetHandle("DisplayTask");
    
    for (int i = 0; i < 3; i++) {
        if (tasks[i] != NULL) {
            perfStats.minFreeStack[i] = uxTaskGetStackHighWaterMark(tasks[i]);
        }
    }
    
    // 简单的CPU使用率估算 (基于空闲任务运行时间)
    static uint32_t lastIdleTime = 0;
    static uint32_t lastTotalTime = 0;
    
    uint32_t idleTime = 0; // TODO: 获取空闲任务运行时间
    uint32_t totalTime = xTaskGetTickCount();
    
    if (totalTime > lastTotalTime) {
        uint32_t idleDelta = idleTime - lastIdleTime;
        uint32_t totalDelta = totalTime - lastTotalTime;
        
        if (totalDelta > 0) {
            perfStats.cpuUsage = 100 - ((idleDelta * 100) / totalDelta);
        }
    }
    
    lastIdleTime = idleTime;
    lastTotalTime = totalTime;
}

/**
 * @brief 打印性能统计 (通过OLED显示)
 */
void Performance_PrintStats(void)
{
    // 这个函数可以用于调试时显示性能数据
    // 在OLED上显示关键性能指标
}
