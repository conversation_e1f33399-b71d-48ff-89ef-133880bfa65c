#include "guitar_buzzer.h"
#include "guitar.h"
#include "tim.h"

/**
 * @brief 无源蜂鸣器专用音频控制模块
 * 
 * 无源蜂鸣器的特点：
 * 1. 只响应频率变化，不响应占空比变化
 * 2. 音量控制需要特殊方法
 * 3. 需要方波驱动信号
 */

/* 无源蜂鸣器音量控制方法 */
typedef enum {
    BUZZER_VOLUME_ON_OFF = 0,    // 开关控制（简单）
    BUZZER_VOLUME_PWM_BURST,     // PWM突发控制
    BUZZER_VOLUME_FREQ_MOD       // 频率调制控制
} BuzzerVolumeMethod_t;

static BuzzerVolumeMethod_t volumeMethod = BUZZER_VOLUME_ON_OFF;

/**
 * @brief 无源蜂鸣器播放音符（优化版）
 * @param frequency 频率 (Hz)
 */
void Guitar_BuzzerPlayNote(float frequency)
{
    if (frequency <= 0.0f) {
        Guitar_BuzzerStop();
        return;
    }
    
    // 获取定时器时钟
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }
    
    // 计算PWM周期
    double precisePeriod = (double)timerClock / (double)frequency;
    uint32_t period = (uint32_t)(precisePeriod + 0.5);
    
    // 边界检查
    if (period > 65535) period = 65535;
    if (period < 50) period = 50;  // 无源蜂鸣器最小周期
    
    // 设置PWM参数 - 固定50%占空比
    __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2);
    htim1.Instance->EGR = TIM_EGR_UG;
    
    guitarStatus.currentNote = (uint16_t)frequency;
}

/**
 * @brief 停止无源蜂鸣器
 */
void Guitar_BuzzerStop(void)
{
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);
    guitarStatus.currentNote = 0;
}

/**
 * @brief 无源蜂鸣器音量控制（PWM突发方法）
 * @param frequency 基础频率
 * @param volume 音量 (0-100)
 */
void Guitar_BuzzerPlayWithVolume(float frequency, uint8_t volume)
{
    if (volume == 0) {
        Guitar_BuzzerStop();
        return;
    }
    
    if (volume >= 100) {
        // 最大音量：连续播放
        Guitar_BuzzerPlayNote(frequency);
        return;
    }
    
    // 中等音量：使用PWM突发控制
    // 原理：在短时间内开关PWM来模拟音量变化
    uint32_t onTime = volume;      // 开启时间 (ms)
    uint32_t offTime = 100 - volume; // 关闭时间 (ms)
    
    // 播放一个突发周期
    Guitar_BuzzerPlayNote(frequency);
    HAL_Delay(onTime);
    Guitar_BuzzerStop();
    HAL_Delay(offTime);
}

/**
 * @brief 测试无源蜂鸣器频率响应
 */
void Guitar_BuzzerFrequencyTest(void)
{
    // 测试频率范围：100Hz - 4000Hz
    uint16_t testFreqs[] = {100, 200, 400, 800, 1600, 3200};
    uint8_t freqCount = sizeof(testFreqs) / sizeof(uint16_t);
    
    for (uint8_t i = 0; i < freqCount; i++) {
        Guitar_BuzzerPlayNote((float)testFreqs[i]);
        HAL_Delay(1000); // 每个频率播放1秒
        Guitar_BuzzerStop();
        HAL_Delay(200);  // 间隔200ms
    }
}

/**
 * @brief 播放音阶测试（无源蜂鸣器版）
 */
void Guitar_BuzzerPlayScale(void)
{
    // Do Re Mi Fa So La Si Do
    float scaleFreqs[] = {
        261.63f, // C4 - Do
        293.66f, // D4 - Re
        329.63f, // E4 - Mi
        349.23f, // F4 - Fa
        392.00f, // G4 - So
        440.00f, // A4 - La
        493.88f, // B4 - Si
        523.25f  // C5 - Do
    };
    
    for (uint8_t i = 0; i < 8; i++) {
        Guitar_BuzzerPlayNote(scaleFreqs[i]);
        HAL_Delay(800); // 每个音符800ms
        Guitar_BuzzerStop();
        HAL_Delay(100); // 间隔100ms
    }
}

/**
 * @brief 无源蜂鸣器音量测试
 */
void Guitar_BuzzerVolumeTest(void)
{
    float testFreq = 440.0f; // A4音符
    
    // 测试不同音量级别
    for (uint8_t volume = 20; volume <= 100; volume += 20) {
        // 播放5次突发
        for (uint8_t burst = 0; burst < 5; burst++) {
            Guitar_BuzzerPlayWithVolume(testFreq, volume);
        }
        HAL_Delay(500); // 音量级别间隔
    }
}

/**
 * @brief 检查无源蜂鸣器连接
 */
void Guitar_BuzzerConnectionTest(void)
{
    // 播放简单的测试音调
    uint16_t testFreqs[] = {500, 1000, 1500, 2000};
    
    for (uint8_t i = 0; i < 4; i++) {
        Guitar_BuzzerPlayNote((float)testFreqs[i]);
        HAL_Delay(500);
        Guitar_BuzzerStop();
        HAL_Delay(200);
    }
}

/**
 * @brief 设置无源蜂鸣器音量控制方法
 * @param method 音量控制方法
 */
void Guitar_BuzzerSetVolumeMethod(BuzzerVolumeMethod_t method)
{
    volumeMethod = method;
}

/**
 * @brief 无源蜂鸣器播放旋律
 * @param melody 旋律频率数组
 * @param durations 音符持续时间数组
 * @param noteCount 音符数量
 */
void Guitar_BuzzerPlayMelody(const float* melody, const uint16_t* durations, uint8_t noteCount)
{
    for (uint8_t i = 0; i < noteCount; i++) {
        if (melody[i] > 0) {
            Guitar_BuzzerPlayNote(melody[i]);
            HAL_Delay(durations[i]);
        } else {
            Guitar_BuzzerStop();
            HAL_Delay(durations[i]);
        }
        
        // 音符间短暂停顿
        Guitar_BuzzerStop();
        HAL_Delay(50);
    }
}

/**
 * @brief 播放小星星（无源蜂鸣器版）
 */
void Guitar_BuzzerPlayTwinkleStar(void)
{
    // 小星星旋律：C C G G A A G F F E E D D C
    float melody[] = {
        261.63f, 261.63f, 392.00f, 392.00f, 440.00f, 440.00f, 392.00f,
        349.23f, 349.23f, 329.63f, 329.63f, 293.66f, 293.66f, 261.63f
    };
    
    uint16_t durations[] = {
        500, 500, 500, 500, 500, 500, 1000,
        500, 500, 500, 500, 500, 500, 1000
    };
    
    Guitar_BuzzerPlayMelody(melody, durations, 14);
}

/**
 * @brief 完整的无源蜂鸣器测试序列
 */
void Guitar_BuzzerFullTest(void)
{
    // 1. 连接测试
    Guitar_BuzzerConnectionTest();
    HAL_Delay(1000);
    
    // 2. 频率测试
    Guitar_BuzzerFrequencyTest();
    HAL_Delay(1000);
    
    // 3. 音阶测试
    Guitar_BuzzerPlayScale();
    HAL_Delay(1000);
    
    // 4. 音量测试
    Guitar_BuzzerVolumeTest();
    HAL_Delay(1000);
    
    // 5. 旋律测试
    Guitar_BuzzerPlayTwinkleStar();
}
