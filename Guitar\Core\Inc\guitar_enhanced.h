#ifndef __GUITAR_ENHANCED_H
#define __GUITAR_ENHANCED_H

#include "guitar.h"

/* 扩展功能 */

/* 和弦定义 */
typedef struct {
    char name[8];           // 和弦名称
    uint16_t notes[4];      // 最多4个音符
    uint8_t noteCount;      // 音符数量
} Chord_t;

/* 预设和弦 */
extern const Chord_t chords[];
extern const uint8_t chordCount;

/* 节拍器相关 */
typedef struct {
    uint16_t bpm;           // 每分钟节拍数
    uint8_t isRunning;      // 是否运行
    uint8_t currentBeat;    // 当前节拍
    uint8_t timeSignature;  // 拍号 (如4表示4/4拍)
} Metronome_t;

extern Metronome_t metronome;

/* 录制播放功能 */
#define MAX_RECORDED_NOTES 32
typedef struct {
    uint16_t frequency;
    uint16_t duration;
    uint32_t timestamp;
} RecordedNote_t;

typedef struct {
    RecordedNote_t notes[MAX_RECORDED_NOTES];
    uint8_t noteCount;
    uint8_t isRecording;
    uint8_t isPlaying;
    uint32_t startTime;
} Recorder_t;

extern Recorder_t recorder;

/* 功能函数声明 */
void Guitar_PlayChord(uint8_t chordIndex);
void Guitar_StartMetronome(void);
void Guitar_StopMetronome(void);
void Guitar_SetBPM(uint16_t bpm);
void Guitar_StartRecording(void);
void Guitar_StopRecording(void);
void Guitar_PlayRecording(void);
void Guitar_ClearRecording(void);
void Guitar_NextMode(void);
void Guitar_PreviousMode(void);
void Guitar_ChangeOctave(int8_t change);
void Guitar_ChangeVolume(int8_t change);

/* 音效增强 */
void Guitar_PlayNoteWithEffect(uint16_t frequency, uint16_t duration, uint8_t effect);
void Guitar_PlayArpeggio(const uint16_t* notes, uint8_t count, uint16_t speed);

#endif /* __GUITAR_ENHANCED_H */
