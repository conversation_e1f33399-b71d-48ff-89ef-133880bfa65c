#include "guitar_effects.h"
#include "cmsis_os.h"
#include "tim.h"
#include <math.h>

/* 全局变量 */
PlayingTechnique_t playingTech = {0};

/**
 * @brief 初始化音效系统
 */
void Guitar_InitEffects(void)
{
    playingTech.sustainEnabled = 0;
    playingTech.vibratoEnabled = 0;
    playingTech.currentEffect = EFFECT_NONE;
    playingTech.attackTime = 10;  // 10ms起音
    playingTech.releaseTime = 50; // 50ms释放
}

/**
 * @brief 播放颤音效果
 * @param frequency 基础频率
 * @param duration 持续时间
 * @param vibratoSpeed 颤音速度 (1-10)
 */
void Guitar_PlayVibratoNote(uint16_t frequency, uint16_t duration, uint8_t vibratoSpeed)
{
    if (vibratoSpeed == 0) vibratoSpeed = 5;
    
    uint32_t startTime = HAL_GetTick();
    uint32_t endTime = startTime + duration;
    
    // 颤音参数
    float vibratoRate = vibratoSpeed * 0.5f; // Hz
    float vibratoDepth = frequency * 0.02f;  // 2%的频率变化
    
    while (HAL_GetTick() < endTime) {
        uint32_t currentTime = HAL_GetTick() - startTime;
        
        // 计算颤音偏移
        float vibratoOffset = sinf(2.0f * 3.14159f * vibratoRate * currentTime / 1000.0f);
        uint16_t modulatedFreq = frequency + (uint16_t)(vibratoDepth * vibratoOffset);
        
        // 应用调制后的频率
        Guitar_UltraFastSwitchNote(0); // 先获取按键索引，简化处理
        
        osDelay(10); // 10ms更新间隔
    }
}

/**
 * @brief 播放弯音效果
 * @param fromFreq 起始频率
 * @param toFreq 目标频率
 * @param duration 弯音持续时间
 */
void Guitar_PlayBendNote(uint16_t fromFreq, uint16_t toFreq, uint16_t duration)
{
    if (duration == 0) duration = 500;
    
    uint32_t startTime = HAL_GetTick();
    uint32_t endTime = startTime + duration;
    uint16_t steps = duration / 10; // 每10ms一步
    
    for (uint16_t step = 0; step <= steps; step++) {
        if (HAL_GetTick() >= endTime) break;
        
        // 线性插值计算当前频率
        float progress = (float)step / steps;
        uint16_t currentFreq = fromFreq + (uint16_t)((toFreq - fromFreq) * progress);
        
        // 直接设置频率
        Guitar_FastSwitchFrequency(currentFreq);
        
        osDelay(10);
    }
}

/**
 * @brief 播放滑音效果
 * @param fromFreq 起始频率  
 * @param toFreq 目标频率
 * @param duration 滑音持续时间
 */
void Guitar_PlaySlideNote(uint16_t fromFreq, uint16_t toFreq, uint16_t duration)
{
    if (duration == 0) duration = 200;
    
    uint32_t startTime = HAL_GetTick();
    uint16_t steps = duration / 5; // 每5ms一步，更平滑
    
    for (uint16_t step = 0; step <= steps; step++) {
        // 使用平滑曲线而非线性
        float progress = (float)step / steps;
        progress = progress * progress * (3.0f - 2.0f * progress); // 平滑曲线
        
        uint16_t currentFreq = fromFreq + (uint16_t)((toFreq - fromFreq) * progress);
        Guitar_FastSwitchFrequency(currentFreq);
        
        osDelay(5);
    }
}

/**
 * @brief 设置延音功能
 * @param enabled 是否开启延音
 */
void Guitar_SetSustain(uint8_t enabled)
{
    playingTech.sustainEnabled = enabled;
}

/**
 * @brief 设置起音和释放时间
 * @param attackMs 起音时间 (ms)
 * @param releaseMs 释放时间 (ms)
 */
void Guitar_SetAttackRelease(uint8_t attackMs, uint8_t releaseMs)
{
    playingTech.attackTime = attackMs;
    playingTech.releaseTime = releaseMs;
}

/**
 * @brief 应用音效到音符
 * @param baseFreq 基础频率
 * @param effect 音效参数
 */
void Guitar_ApplyEffect(uint16_t baseFreq, EffectParams_t* effect)
{
    if (!effect || effect->type == EFFECT_NONE) {
        Guitar_FastSwitchFrequency(baseFreq);
        return;
    }
    
    switch (effect->type) {
        case EFFECT_VIBRATO:
            Guitar_PlayVibratoNote(baseFreq, effect->duration, effect->speed);
            break;
            
        case EFFECT_BEND:
            {
                uint16_t bendTarget = baseFreq + (baseFreq * effect->intensity / 200);
                Guitar_PlayBendNote(baseFreq, bendTarget, effect->duration);
            }
            break;
            
        case EFFECT_SLIDE:
            {
                uint16_t slideTarget = baseFreq + (baseFreq * effect->intensity / 100);
                Guitar_PlaySlideNote(baseFreq, slideTarget, effect->duration);
            }
            break;
            
        case EFFECT_STACCATO:
            // 断奏：短促演奏
            Guitar_FastSwitchFrequency(baseFreq);
            osDelay(effect->duration / 3); // 只演奏1/3时间
            Guitar_FastSwitchFrequency(0);
            break;
            
        default:
            Guitar_FastSwitchFrequency(baseFreq);
            break;
    }
}
