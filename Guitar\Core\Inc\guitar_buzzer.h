#ifndef __GUITAR_BUZZER_H
#define __GUITAR_BUZZER_H

#include "guitar.h"

/**
 * @brief 无源蜂鸣器专用音频控制模块
 * 
 * 无源蜂鸣器特点：
 * - 只响应频率变化，不响应占空比变化
 * - 需要方波驱动信号
 * - 音量控制需要特殊方法
 */

/* 无源蜂鸣器音量控制方法 */
typedef enum {
    BUZZER_VOLUME_ON_OFF = 0,    // 开关控制（简单）
    BUZZER_VOLUME_PWM_BURST,     // PWM突发控制
    BUZZER_VOLUME_FREQ_MOD       // 频率调制控制
} BuzzerVolumeMethod_t;

/* 函数声明 */

/**
 * @brief 无源蜂鸣器播放音符（优化版）
 * @param frequency 频率 (Hz)
 */
void Guitar_BuzzerPlayNote(float frequency);

/**
 * @brief 停止无源蜂鸣器
 */
void Guitar_BuzzerStop(void);

/**
 * @brief 无源蜂鸣器音量控制（PWM突发方法）
 * @param frequency 基础频率
 * @param volume 音量 (0-100)
 */
void Guitar_BuzzerPlayWithVolume(float frequency, uint8_t volume);

/**
 * @brief 测试无源蜂鸣器频率响应
 * 播放不同频率测试蜂鸣器响应范围
 */
void Guitar_BuzzerFrequencyTest(void);

/**
 * @brief 播放音阶测试（无源蜂鸣器版）
 * 播放Do Re Mi Fa So La Si Do音阶
 */
void Guitar_BuzzerPlayScale(void);

/**
 * @brief 无源蜂鸣器音量测试
 * 测试不同音量级别的效果
 */
void Guitar_BuzzerVolumeTest(void);

/**
 * @brief 检查无源蜂鸣器连接
 * 播放简单测试音调验证连接
 */
void Guitar_BuzzerConnectionTest(void);

/**
 * @brief 设置无源蜂鸣器音量控制方法
 * @param method 音量控制方法
 */
void Guitar_BuzzerSetVolumeMethod(BuzzerVolumeMethod_t method);

/**
 * @brief 无源蜂鸣器播放旋律
 * @param melody 旋律频率数组
 * @param durations 音符持续时间数组
 * @param noteCount 音符数量
 */
void Guitar_BuzzerPlayMelody(const float* melody, const uint16_t* durations, uint8_t noteCount);

/**
 * @brief 播放小星星（无源蜂鸣器版）
 * 演示旋律播放功能
 */
void Guitar_BuzzerPlayTwinkleStar(void);

/**
 * @brief 完整的无源蜂鸣器测试序列
 * 执行所有测试功能
 */
void Guitar_BuzzerFullTest(void);

/* 便捷宏定义 */
#define BUZZER_PLAY_C4()    Guitar_BuzzerPlayNote(261.63f)
#define BUZZER_PLAY_D4()    Guitar_BuzzerPlayNote(293.66f)
#define BUZZER_PLAY_E4()    Guitar_BuzzerPlayNote(329.63f)
#define BUZZER_PLAY_F4()    Guitar_BuzzerPlayNote(349.23f)
#define BUZZER_PLAY_G4()    Guitar_BuzzerPlayNote(392.00f)
#define BUZZER_PLAY_A4()    Guitar_BuzzerPlayNote(440.00f)

#define BUZZER_STOP()       Guitar_BuzzerStop()

/* 常用频率定义 */
#define BUZZER_FREQ_LOW     200.0f    // 低音测试频率
#define BUZZER_FREQ_MID     1000.0f   // 中音测试频率
#define BUZZER_FREQ_HIGH    3000.0f   // 高音测试频率

#endif /* __GUITAR_BUZZER_H */
