#ifndef __GUITAR_UI_H
#define __GUITAR_UI_H

#include "guitar.h"

/* 界面模式 */
typedef enum {
    UI_MODE_MAIN = 0,     // 主界面
    UI_MODE_TUNER,        // 调音器
    UI_MODE_CHORDS,       // 和弦界面
    UI_MODE_SCALES,       // 音阶界面
    UI_MODE_EFFECTS,      // 音效界面
    UI_MODE_RECORDER,     // 录音界面
    UI_MODE_METRONOME,    // 节拍器界面
    UI_MODE_SETTINGS,     // 设置界面
    UI_MODE_PRACTICE      // 练习界面
} UIMode_t;

/* 菜单项结构 */
typedef struct {
    char name[12];        // 菜单项名称
    uint8_t icon;         // 图标ID
    void (*action)(void); // 动作函数
} MenuItem_t;

/* 用户界面状态 */
typedef struct {
    UIMode_t currentMode;     // 当前模式
    uint8_t selectedItem;     // 选中项
    uint8_t menuDepth;        // 菜单深度
    uint8_t brightness;       // 屏幕亮度
    uint8_t autoSleep;        // 自动休眠 (分钟)
    uint32_t lastActivity;    // 最后活动时间
    uint8_t needsUpdate;      // 需要更新显示
} UIState_t;

/* 动画效果 */
typedef struct {
    uint8_t isActive;         // 动画激活
    uint8_t type;             // 动画类型
    uint8_t progress;         // 动画进度 (0-100)
    uint32_t startTime;       // 开始时间
    uint16_t duration;        // 持续时间
} Animation_t;

/* 全局变量 */
extern UIState_t uiState;
extern Animation_t currentAnim;

/* 函数声明 */
void Guitar_InitUI(void);
void Guitar_UpdateUI(void);
void Guitar_HandleButtonPress(uint8_t buttonIndex, uint8_t longPress);
void Guitar_ChangeMode(UIMode_t newMode);
void Guitar_DrawMenu(const MenuItem_t* menu, uint8_t itemCount);
void Guitar_ShowMessage(const char* message, uint16_t duration);

/* 界面绘制函数 */
void Guitar_DrawMainScreen(void);
void Guitar_DrawTunerScreen(void);
void Guitar_DrawChordsScreen(void);
void Guitar_DrawScalesScreen(void);
void Guitar_DrawEffectsScreen(void);
void Guitar_DrawRecorderScreen(void);
void Guitar_DrawMetronomeScreen(void);
void Guitar_DrawSettingsScreen(void);
void Guitar_DrawPracticeScreen(void);

/* 动画效果 */
void Guitar_StartAnimation(uint8_t type, uint16_t duration);
void Guitar_UpdateAnimation(void);

/* 实用工具 */
void Guitar_DrawProgressBar(uint8_t x, uint8_t y, uint8_t width, uint8_t progress);
void Guitar_DrawIcon(uint8_t x, uint8_t y, uint8_t iconId);
void Guitar_DrawKeyboard(uint8_t pressedKeys);

/* 按键处理 */
#define LONG_PRESS_TIME 1000  // 长按时间 (ms)

/* 动画类型 */
#define ANIM_FADE_IN    0
#define ANIM_FADE_OUT   1
#define ANIM_SLIDE_LEFT 2
#define ANIM_SLIDE_RIGHT 3
#define ANIM_BOUNCE     4

#endif /* __GUITAR_UI_H */
