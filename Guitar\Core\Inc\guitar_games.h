#ifndef __GUITAR_GAMES_H
#define __GUITAR_GAMES_H

#include "guitar.h"

/* 游戏类型 */
typedef enum {
    GAME_SIMON = 0,       // 西蒙记忆游戏
    GAME_RHYTHM_HERO,     // 节奏英雄
    GAME_NOTE_GUESS,      // 音符猜测
    GAME_SCALE_RACE,      // 音阶竞速
    GAME_CHORD_MATCH      // 和弦匹配
} GameType_t;

/* 游戏状态 */
typedef struct {
    GameType_t currentGame;   // 当前游戏
    uint8_t isActive;         // 是否激活
    uint8_t level;            // 游戏级别
    uint16_t score;           // 当前分数
    uint16_t highScore;       // 最高分数
    uint8_t lives;            // 生命值
    uint32_t gameTime;        // 游戏时间
} GameState_t;

/* 西蒙游戏数据 */
typedef struct {
    uint8_t sequence[16];     // 序列 (最多16个音符)
    uint8_t sequenceLength;   // 序列长度
    uint8_t playerPos;        // 玩家位置
    uint8_t gameSpeed;        // 游戏速度
} SimonGame_t;

/* 节奏英雄游戏 */
typedef struct {
    uint8_t notes[32];        // 音符序列
    uint32_t timings[32];     // 时间序列
    uint8_t noteCount;        // 音符数量
    uint8_t currentNote;      // 当前音符
    uint32_t songStartTime;   // 歌曲开始时间
} RhythmHeroGame_t;

/* 全局变量 */
extern GameState_t gameState;
extern SimonGame_t simonGame;
extern RhythmHeroGame_t rhythmGame;

/* 函数声明 */
void Guitar_InitGames(void);
void Guitar_StartGame(GameType_t gameType);
void Guitar_StopGame(void);
void Guitar_UpdateGame(void);
void Guitar_ProcessGameInput(uint8_t keyPressed);

/* 西蒙游戏 */
void Guitar_StartSimonGame(void);
void Guitar_PlaySimonSequence(void);
void Guitar_CheckSimonInput(uint8_t key);
void Guitar_NextSimonLevel(void);

/* 节奏英雄游戏 */
void Guitar_StartRhythmHero(void);
void Guitar_LoadSong(uint8_t songIndex);
void Guitar_UpdateRhythmGame(void);
uint8_t Guitar_CheckRhythmTiming(uint32_t inputTime);

/* 音符猜测游戏 */
void Guitar_StartNoteGuess(void);
void Guitar_PlayRandomNote(void);
uint8_t Guitar_CheckGuess(uint8_t guessedNote);

/* 游戏界面 */
void Guitar_DrawGameScreen(void);
void Guitar_ShowGameScore(void);
void Guitar_ShowGameOver(void);

/* 成就系统 */
typedef struct {
    char name[16];            // 成就名称
    char description[32];     // 描述
    uint8_t unlocked;         // 是否解锁
    uint16_t requirement;     // 解锁要求
} Achievement_t;

extern Achievement_t achievements[];
extern const uint8_t achievementCount;

void Guitar_CheckAchievements(void);
void Guitar_UnlockAchievement(uint8_t achievementId);

#endif /* __GUITAR_GAMES_H */
