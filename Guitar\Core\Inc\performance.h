#ifndef __PERFORMANCE_H
#define __PERFORMANCE_H

#include "main.h"

/* 性能监控结构体 */
typedef struct {
    uint32_t keyScanTime;       // 按键扫描耗时 (us)
    uint32_t audioSwitchTime;   // 音频切换耗时 (us)
    uint32_t displayTime;       // 显示更新耗时 (us)
    uint32_t totalResponseTime; // 总响应时间 (us)
    uint32_t cpuUsage;          // CPU使用率 (%)
    uint16_t freeHeapSize;      // 空闲堆大小 (bytes)
    uint16_t minFreeStack[3];   // 各任务最小空闲栈 (words)
} PerformanceStats_t;

/* 外部变量 */
extern PerformanceStats_t perfStats;

/* 函数声明 */
void Performance_Init(void);
void Performance_StartTimer(void);
uint32_t Performance_GetElapsedTime(void);
void Performance_UpdateStats(void);
void Performance_PrintStats(void);

/* 性能监控宏 */
#define PERF_START_TIMER()    Performance_StartTimer()
#define PERF_MEASURE(var)     (var = Performance_GetElapsedTime())

#endif /* __PERFORMANCE_H */
