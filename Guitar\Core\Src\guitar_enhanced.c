#include "guitar_enhanced.h"
#include "cmsis_os.h"

/* 预设和弦定义 */
const Chord_t chords[] = {
    {"C Major", {NOTE_C4, NOTE_E4, NOTE_G4, 0}, 3},
    {"D Minor", {NOTE_D4, NOTE_F4, NOTE_A4, 0}, 3},
    {"E Minor", {NOTE_E4, NOTE_G4, NOTE_B4, 0}, 3},
    {"F Major", {NOTE_F4, NOTE_A4, NOTE_C5, 0}, 3},
    {"G Major", {NOTE_G4, NOTE_B4, NOTE_D4, 0}, 3},
    {"A Minor", {NOTE_A4, NOTE_C5, NOTE_E4, 0}, 3}
};

const uint8_t chordCount = sizeof(chords) / sizeof(Chord_t);

/* 节拍器 */
Metronome_t metronome = {
    .bpm = 120,
    .isRunning = 0,
    .currentBeat = 1,
    .timeSignature = 4
};

/* 录制器 */
Recorder_t recorder = {0};

/**
 * @brief 播放和弦
 * @param chordIndex 和弦索引
 */
void Guitar_PlayChord(uint8_t chordIndex)
{
    if (chordIndex >= chordCount) return;
    
    const Chord_t* chord = &chords[chordIndex];
    
    // 依次播放和弦中的音符（琶音效果）
    for (uint8_t i = 0; i < chord->noteCount; i++) {
        if (chord->notes[i] > 0) {
            Guitar_PlayNote(chord->notes[i], 200);
            osDelay(150); // 音符间隔
        }
    }
}

/**
 * @brief 开始节拍器
 */
void Guitar_StartMetronome(void)
{
    metronome.isRunning = 1;
    metronome.currentBeat = 1;
}

/**
 * @brief 停止节拍器
 */
void Guitar_StopMetronome(void)
{
    metronome.isRunning = 0;
}

/**
 * @brief 设置节拍器BPM
 * @param bpm 每分钟节拍数
 */
void Guitar_SetBPM(uint16_t bpm)
{
    if (bpm >= 60 && bpm <= 200) {
        metronome.bpm = bpm;
    }
}

/**
 * @brief 开始录音
 */
void Guitar_StartRecording(void)
{
    recorder.isRecording = 1;
    recorder.isPlaying = 0;
    recorder.noteCount = 0;
    recorder.startTime = HAL_GetTick();
}

/**
 * @brief 停止录音
 */
void Guitar_StopRecording(void)
{
    recorder.isRecording = 0;
}

/**
 * @brief 播放录音
 */
void Guitar_PlayRecording(void)
{
    if (recorder.noteCount == 0) return;
    
    recorder.isPlaying = 1;
    recorder.startTime = HAL_GetTick();
    
    // 在后台任务中播放录音
    // 这里可以通过队列发送播放命令
}

/**
 * @brief 清除录音
 */
void Guitar_ClearRecording(void)
{
    recorder.noteCount = 0;
    recorder.isRecording = 0;
    recorder.isPlaying = 0;
}

/**
 * @brief 切换到下一个模式
 */
void Guitar_NextMode(void)
{
    guitarStatus.mode = (guitarStatus.mode + 1) % 3;
}

/**
 * @brief 切换到上一个模式
 */
void Guitar_PreviousMode(void)
{
    if (guitarStatus.mode == 0) {
        guitarStatus.mode = 2;
    } else {
        guitarStatus.mode--;
    }
}

/**
 * @brief 改变八度
 * @param change 变化量 (+1或-1)
 */
void Guitar_ChangeOctave(int8_t change)
{
    int16_t newOctave = guitarStatus.octave + change;
    if (newOctave >= 3 && newOctave <= 6) {
        guitarStatus.octave = newOctave;
    }
}

/**
 * @brief 改变音量
 * @param change 变化量
 */
void Guitar_ChangeVolume(int8_t change)
{
    int16_t newVolume = guitarStatus.volume + change;
    if (newVolume >= 0 && newVolume <= 100) {
        guitarStatus.volume = newVolume;
    }
}

/**
 * @brief 播放琶音
 * @param notes 音符数组
 * @param count 音符数量
 * @param speed 速度(ms间隔)
 */
void Guitar_PlayArpeggio(const uint16_t* notes, uint8_t count, uint16_t speed)
{
    for (uint8_t i = 0; i < count; i++) {
        if (notes[i] > 0) {
            Guitar_PlayNote(notes[i], speed - 50);
            osDelay(speed);
        }
    }
}
