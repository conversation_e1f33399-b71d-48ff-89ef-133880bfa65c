#ifndef __GUITAR_RHYTHM_H
#define __GUITAR_RHYTHM_H

#include "guitar.h"

/* 节奏模式 */
typedef enum {
    RHYTHM_SIMPLE = 0,    // 简单节拍
    RHYTHM_SWING,         // 摇摆节拍
    RHYTHM_LATIN,         // 拉丁节奏
    RHYTHM_ROCK,          // 摇滚节奏
    RHYTHM_BLUES,         // 布鲁斯节奏
    RHYTHM_CUSTOM         // 自定义节奏
} RhythmPattern_t;

/* 节拍器增强版 */
typedef struct {
    uint16_t bpm;             // 每分钟节拍数 (60-200)
    uint8_t timeSignature[2]; // 拍号 [分子, 分母] 如 [4,4]
    RhythmPattern_t pattern;  // 节奏模式
    uint8_t isRunning;        // 是否运行
    uint8_t currentBeat;      // 当前节拍位置
    uint8_t accentBeats[8];   // 重音节拍位置
    uint8_t volume;           // 节拍器音量
    uint16_t clickFreq;       // 节拍音频率
    uint16_t accentFreq;      // 重音频率
} AdvancedMetronome_t;

/* 节奏训练模式 */
typedef struct {
    uint8_t isActive;         // 是否激活训练
    uint8_t targetAccuracy;   // 目标准确度 (%)
    uint8_t currentAccuracy;  // 当前准确度
    uint16_t totalBeats;      // 总节拍数
    uint16_t correctBeats;    // 正确节拍数
    uint32_t lastBeatTime;    // 上次节拍时间
    uint8_t difficultyLevel;  // 难度级别 (1-5)
} RhythmTraining_t;

/* 全局变量 */
extern AdvancedMetronome_t advMetronome;
extern RhythmTraining_t rhythmTraining;

/* 函数声明 */
void Guitar_InitRhythmSystem(void);
void Guitar_StartAdvancedMetronome(void);
void Guitar_StopAdvancedMetronome(void);
void Guitar_SetRhythmPattern(RhythmPattern_t pattern);
void Guitar_SetTimeSignature(uint8_t numerator, uint8_t denominator);
void Guitar_SetAccentBeats(uint8_t* beats, uint8_t count);

/* 节奏训练功能 */
void Guitar_StartRhythmTraining(uint8_t difficulty);
void Guitar_StopRhythmTraining(void);
uint8_t Guitar_CheckBeatAccuracy(uint32_t userBeatTime);
void Guitar_UpdateRhythmStats(void);

/* 节拍器任务回调 */
void Guitar_MetronomeCallback(void);

#endif /* __GUITAR_RHYTHM_H */
