Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xb.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xb.o(RESET) refers to cmsis_os2.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xb.o(RESET) refers to stm32f1xx_it.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_PowerOnInit) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_PowerOnInit) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Init) for OLED_Init
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_PowerOnInit) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_PowerOnInit) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F6x8
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_CheckBufferIntegrity) for OLED_CheckBufferIntegrity
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteCommand) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteCommand) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_WriteCommand) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_WriteData) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    oled.o(i.OLED_WriteData) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteData) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_WriteData) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteData) refers to i2c.o(.bss) for hi2c1
    guitar.o(i.Guitar_CheckKeyCombo) refers to guitar.o(i.Guitar_SimpleVibrato) for Guitar_SimpleVibrato
    guitar.o(i.Guitar_CheckKeyCombo) refers to guitar.o(i.Guitar_PlaySimpleChord) for Guitar_PlaySimpleChord
    guitar.o(i.Guitar_CheckKeyCombo) refers to guitar.o(i.Guitar_SimpleMetronome) for Guitar_SimpleMetronome
    guitar.o(i.Guitar_CheckKeyCombo) refers to guitar.o(i.Guitar_GetNoteFrequency) for Guitar_GetNoteFrequency
    guitar.o(i.Guitar_CheckKeyCombo) refers to guitar.o(i.Guitar_UltraFastSwitchNote) for Guitar_UltraFastSwitchNote
    guitar.o(i.Guitar_CheckKeyCombo) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_DebugFrequencies) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    guitar.o(i.Guitar_DebugFrequencies) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    guitar.o(i.Guitar_DebugFrequencies) refers to guitar.o(i.Guitar_GetPreciseNoteFrequency) for Guitar_GetPreciseNoteFrequency
    guitar.o(i.Guitar_FastSwitchFrequency) refers to guitar.o(i.Guitar_SetFrequency) for Guitar_SetFrequency
    guitar.o(i.Guitar_GetNoteFrequency) refers to guitar.o(i.Guitar_GetPreciseNoteFrequency) for Guitar_GetPreciseNoteFrequency
    guitar.o(i.Guitar_GetNoteFrequency) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    guitar.o(i.Guitar_GetNoteFrequency) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    guitar.o(i.Guitar_GetPreciseNoteFrequency) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    guitar.o(i.Guitar_GetPreciseNoteFrequency) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    guitar.o(i.Guitar_GetPreciseNoteFrequency) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    guitar.o(i.Guitar_GetPreciseNoteFrequency) refers to guitar.o(.constdata) for .constdata
    guitar.o(i.Guitar_GetPreciseNoteFrequency) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_Init) refers to guitar.o(i.Guitar_PrecomputePeriods) for Guitar_PrecomputePeriods
    guitar.o(i.Guitar_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    guitar.o(i.Guitar_Init) refers to guitar.o(i.Guitar_StopNote) for Guitar_StopNote
    guitar.o(i.Guitar_Init) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_Init) refers to tim.o(.bss) for htim1
    guitar.o(i.Guitar_IsKeyPressed) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    guitar.o(i.Guitar_PlayCalibrationSequence) refers to guitar.o(i.Guitar_GetPreciseNoteFrequency) for Guitar_GetPreciseNoteFrequency
    guitar.o(i.Guitar_PlayCalibrationSequence) refers to guitar.o(i.Guitar_SetPreciseFrequency) for Guitar_SetPreciseFrequency
    guitar.o(i.Guitar_PlayCalibrationSequence) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    guitar.o(i.Guitar_PlayCalibrationSequence) refers to guitar.o(i.Guitar_StopNote) for Guitar_StopNote
    guitar.o(i.Guitar_PlayNote) refers to guitar.o(i.Guitar_SetFrequency) for Guitar_SetFrequency
    guitar.o(i.Guitar_PlayNote) refers to guitar.o(i.Guitar_SetVolume) for Guitar_SetVolume
    guitar.o(i.Guitar_PlayNote) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    guitar.o(i.Guitar_PlayNote) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_PlaySimpleChord) refers to guitar.o(i.Guitar_SetFrequency) for Guitar_SetFrequency
    guitar.o(i.Guitar_PlaySimpleChord) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    guitar.o(i.Guitar_PlayTuningA4) refers to guitar.o(i.Guitar_SetPreciseFrequency) for Guitar_SetPreciseFrequency
    guitar.o(i.Guitar_PrecomputePeriods) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    guitar.o(i.Guitar_PrecomputePeriods) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    guitar.o(i.Guitar_PrecomputePeriods) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    guitar.o(i.Guitar_PrecomputePeriods) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    guitar.o(i.Guitar_PrecomputePeriods) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    guitar.o(i.Guitar_PrecomputePeriods) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    guitar.o(i.Guitar_PrecomputePeriods) refers to guitar.o(.constdata) for .constdata
    guitar.o(i.Guitar_PrecomputePeriods) refers to guitar.o(.bss) for .bss
    guitar.o(i.Guitar_ProcessKeyEvent) refers to cmsis_os2.o(i.osMessageQueuePut) for osMessageQueuePut
    guitar.o(i.Guitar_ProcessKeyEvent) refers to guitar.o(i.Guitar_GetNoteFrequency) for Guitar_GetNoteFrequency
    guitar.o(i.Guitar_ProcessKeyEvent) refers to freertos.o(.data) for AudioCommandQueueHandle
    guitar.o(i.Guitar_ProcessKeyEvent) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_QuickBuzzerTest) refers to guitar.o(i.Guitar_SimpleScaleTest) for Guitar_SimpleScaleTest
    guitar.o(i.Guitar_ScanKeys) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    guitar.o(i.Guitar_ScanKeys) refers to guitar.o(i.Guitar_IsKeyPressed) for Guitar_IsKeyPressed
    guitar.o(i.Guitar_ScanKeys) refers to cmsis_os2.o(i.osMessageQueuePut) for osMessageQueuePut
    guitar.o(i.Guitar_ScanKeys) refers to guitar.o(i.Guitar_CheckKeyCombo) for Guitar_CheckKeyCombo
    guitar.o(i.Guitar_ScanKeys) refers to guitar.o(i.Guitar_StopNote) for Guitar_StopNote
    guitar.o(i.Guitar_ScanKeys) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_ScanKeys) refers to guitar.o(.bss) for .bss
    guitar.o(i.Guitar_ScanKeys) refers to freertos.o(.data) for KeyEventQueueHandle
    guitar.o(i.Guitar_SetFrequency) refers to guitar.o(i.Guitar_StopNote) for Guitar_StopNote
    guitar.o(i.Guitar_SetFrequency) refers to tim.o(.bss) for htim1
    guitar.o(i.Guitar_SetFrequency) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_SetPreciseFrequency) refers to guitar.o(i.Guitar_StopNote) for Guitar_StopNote
    guitar.o(i.Guitar_SetPreciseFrequency) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    guitar.o(i.Guitar_SetPreciseFrequency) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    guitar.o(i.Guitar_SetPreciseFrequency) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    guitar.o(i.Guitar_SetPreciseFrequency) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    guitar.o(i.Guitar_SetPreciseFrequency) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    guitar.o(i.Guitar_SetPreciseFrequency) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    guitar.o(i.Guitar_SetPreciseFrequency) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    guitar.o(i.Guitar_SetPreciseFrequency) refers to tim.o(.bss) for htim1
    guitar.o(i.Guitar_SetPreciseFrequency) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_SetVolume) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_SetVolume) refers to tim.o(.bss) for htim1
    guitar.o(i.Guitar_SimpleMetronome) refers to guitar.o(i.Guitar_SetFrequency) for Guitar_SetFrequency
    guitar.o(i.Guitar_SimpleMetronome) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    guitar.o(i.Guitar_SimpleScaleTest) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    guitar.o(i.Guitar_SimpleScaleTest) refers to guitar.o(i.Guitar_SetFrequency) for Guitar_SetFrequency
    guitar.o(i.Guitar_SimpleScaleTest) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    guitar.o(i.Guitar_SimpleScaleTest) refers to guitar.o(i.Guitar_StopNote) for Guitar_StopNote
    guitar.o(i.Guitar_SimpleScaleTest) refers to guitar.o(.constdata) for .constdata
    guitar.o(i.Guitar_SimpleVibrato) refers to guitar.o(i.Guitar_GetNoteFrequency) for Guitar_GetNoteFrequency
    guitar.o(i.Guitar_SimpleVibrato) refers to guitar.o(i.Guitar_SetFrequency) for Guitar_SetFrequency
    guitar.o(i.Guitar_SimpleVibrato) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    guitar.o(i.Guitar_StopNote) refers to tim.o(.bss) for htim1
    guitar.o(i.Guitar_StopNote) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_TestA4Accuracy) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    guitar.o(i.Guitar_TestA4Accuracy) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    guitar.o(i.Guitar_TestA4Accuracy) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    guitar.o(i.Guitar_TestA4Accuracy) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    guitar.o(i.Guitar_TestA4Accuracy) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    guitar.o(i.Guitar_TestA4Accuracy) refers to tim.o(.bss) for htim1
    guitar.o(i.Guitar_TestOLED) refers to oled.o(i.OLED_Clear) for OLED_Clear
    guitar.o(i.Guitar_TestOLED) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    guitar.o(i.Guitar_TestOLED) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    guitar.o(i.Guitar_TestOLED) refers to oled.o(i.OLED_Update) for OLED_Update
    guitar.o(i.Guitar_UltraFastSwitchNote) refers to guitar.o(i.Guitar_GetPreciseNoteFrequency) for Guitar_GetPreciseNoteFrequency
    guitar.o(i.Guitar_UltraFastSwitchNote) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    guitar.o(i.Guitar_UltraFastSwitchNote) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    guitar.o(i.Guitar_UltraFastSwitchNote) refers to tim.o(.bss) for htim1
    guitar.o(i.Guitar_UltraFastSwitchNote) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_UltraFastSwitchNote) refers to guitar.o(.bss) for .bss
    guitar.o(i.Guitar_UpdateDisplay) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    guitar.o(i.Guitar_UpdateDisplay) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    guitar.o(i.Guitar_UpdateDisplay) refers to _printf_dec.o(.text) for _printf_int_dec
    guitar.o(i.Guitar_UpdateDisplay) refers to oled.o(i.OLED_Clear) for OLED_Clear
    guitar.o(i.Guitar_UpdateDisplay) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    guitar.o(i.Guitar_UpdateDisplay) refers to __2sprintf.o(.text) for __2sprintf
    guitar.o(i.Guitar_UpdateDisplay) refers to strlen.o(.text) for strlen
    guitar.o(i.Guitar_UpdateDisplay) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    guitar.o(i.Guitar_UpdateDisplay) refers to strcpy.o(.text) for strcpy
    guitar.o(i.Guitar_UpdateDisplay) refers to strcat.o(.text) for strcat
    guitar.o(i.Guitar_UpdateDisplay) refers to oled.o(i.OLED_Update) for OLED_Update
    guitar.o(i.Guitar_UpdateDisplay) refers to guitar.o(.data) for .data
    guitar.o(i.Guitar_UpdateDisplay) refers to guitar.o(.constdata) for .constdata
    guitar.o(i.Guitar_VerifyTimerConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    guitar.o(.constdata) refers to guitar.o(.conststring) for .conststring
    main.o(i.HAL_TIM_PeriodElapsedCallback) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to guitar.o(i.Guitar_Init) for Guitar_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelInitialize) for osKernelInitialize
    main.o(i.main) refers to freertos.o(i.MX_FREERTOS_Init) for MX_FREERTOS_Init
    main.o(i.main) refers to cmsis_os2.o(i.osKernelStart) for osKernelStart
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osMutexNew) for osMutexNew
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osMessageQueueNew) for osMessageQueueNew
    freertos.o(i.MX_FREERTOS_Init) refers to cmsis_os2.o(i.osThreadNew) for osThreadNew
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.constdata) for .constdata
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(.data) for .data
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartKeyScanTask) for StartKeyScanTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartAudioTask) for StartAudioTask
    freertos.o(i.MX_FREERTOS_Init) refers to freertos.o(i.StartDisplayTask) for StartDisplayTask
    freertos.o(i.StartAudioTask) refers to cmsis_os2.o(i.osMessageQueueGet) for osMessageQueueGet
    freertos.o(i.StartAudioTask) refers to guitar.o(i.Guitar_SetFrequency) for Guitar_SetFrequency
    freertos.o(i.StartAudioTask) refers to guitar.o(i.Guitar_SetVolume) for Guitar_SetVolume
    freertos.o(i.StartAudioTask) refers to guitar.o(i.Guitar_StopNote) for Guitar_StopNote
    freertos.o(i.StartAudioTask) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    freertos.o(i.StartAudioTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartAudioTask) refers to freertos.o(.data) for .data
    freertos.o(i.StartAudioTask) refers to guitar.o(.data) for guitarStatus
    freertos.o(i.StartDisplayTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(i.StartDisplayTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_PowerOnInit) for OLED_PowerOnInit
    freertos.o(i.StartDisplayTask) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_Clear) for OLED_Clear
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    freertos.o(i.StartDisplayTask) refers to oled.o(i.OLED_Update) for OLED_Update
    freertos.o(i.StartDisplayTask) refers to cmsis_os2.o(i.osMutexAcquire) for osMutexAcquire
    freertos.o(i.StartDisplayTask) refers to guitar.o(i.Guitar_UpdateDisplay) for Guitar_UpdateDisplay
    freertos.o(i.StartDisplayTask) refers to cmsis_os2.o(i.osMutexRelease) for osMutexRelease
    freertos.o(i.StartDisplayTask) refers to freertos.o(.data) for .data
    freertos.o(i.StartKeyScanTask) refers to guitar.o(i.Guitar_ScanKeys) for Guitar_ScanKeys
    freertos.o(i.StartKeyScanTask) refers to cmsis_os2.o(i.osDelay) for osDelay
    freertos.o(.constdata) refers to freertos.o(.conststring) for .conststring
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f1xx_it.o(i.TIM4_IRQHandler) refers to stm32f1xx_hal_timebase_tim.o(.bss) for htim4
    stm32f1xx_hal_msp.o(i.HAL_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig) for HAL_RCC_GetClockConfig
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick) refers to stm32f1xx_hal_timebase_tim.o(.bss) for .bss
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAError) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to main.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_ITError) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f1xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f1xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupClearBitsFromISR) refers to event_groups.o(i.vEventGroupClearBitsCallback) for vEventGroupClearBitsCallback
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupCreateStatic) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to timers.o(i.xTimerPendFunctionCallFromISR) for xTimerPendFunctionCallFromISR
    event_groups.o(i.xEventGroupSetBitsFromISR) refers to event_groups.o(i.vEventGroupSetBitsCallback) for vEventGroupSetBitsCallback
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.pcQueueGetName) refers to queue.o(.bss) for .bss
    queue.o(i.prvCopyDataFromQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to tasks.o(i.xTaskPriorityDisinherit) for xTaskPriorityDisinherit
    queue.o(i.prvInitialiseMutex) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueAddToRegistry) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.vQueueUnregisterQueue) refers to queue.o(.bss) for .bss
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueWaitForMessageRestricted) refers to tasks.o(i.vTaskPlaceOnEventListRestricted) for vTaskPlaceOnEventListRestricted
    queue.o(i.vQueueWaitForMessageRestricted) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueCreateCountingSemaphore) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateCountingSemaphoreStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    queue.o(i.xQueueCreateMutex) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    queue.o(i.xQueueCreateMutexStatic) refers to queue.o(i.prvInitialiseMutex) for prvInitialiseMutex
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericCreateStatic) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSendFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGetMutexHolder) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueGiveMutexRecursive) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeekFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.pvTaskIncrementMutexHeldCount) for pvTaskIncrementMutexHeldCount
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskPriorityInherit) for xTaskPriorityInherit
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPriorityDisinheritAfterTimeout) for vTaskPriorityDisinheritAfterTimeout
    queue.o(i.xQueueTakeMutexRecursive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    queue.o(i.xQueueTakeMutexRecursive) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memset.o(.text) for memset
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferGenericCreateStatic) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyStateClear) for xTaskNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.eTaskGetState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.eTaskGetState) refers to tasks.o(.data) for .data
    tasks.o(i.eTaskGetState) refers to tasks.o(.bss) for .bss
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvAddNewTaskToReadyList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for .data
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvIdleTask) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvIdleTask) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for .bss
    tasks.o(i.prvIdleTask) refers to tasks.o(.data) for .data
    tasks.o(i.prvInitialiseNewTask) refers to aeabi_memset.o(.text) for __aeabi_memset
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvListTasksWithinSingleList) refers to tasks.o(i.vTaskGetInfo) for vTaskGetInfo
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for .data
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for .bss
    tasks.o(i.pvTaskIncrementMutexHeldCount) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyTake) refers to tasks.o(.data) for .data
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskNotifyValueClear) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.uxTaskGetStackHighWaterMark) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.prvListTasksWithinSingleList) for prvListTasksWithinSingleList
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskGetSystemState) refers to tasks.o(.bss) for .bss
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskPriorityGetFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelay) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelayUntil) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    tasks.o(i.vTaskGetInfo) refers to tasks.o(i.prvTaskCheckFreeStackSpace) for prvTaskCheckFreeStackSpace
    tasks.o(i.vTaskGetInfo) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskNotifyGiveFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskNotifyGiveFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventListRestricted) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPriorityDisinheritAfterTimeout) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskStartScheduler) refers to cmsis_os2.o(i.vApplicationGetIdleTaskMemory) for vApplicationGetIdleTaskMemory
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    tasks.o(i.vTaskStartScheduler) refers to timers.o(i.xTimerCreateTimerTask) for xTimerCreateTimerTask
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for .bss
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for .data
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreateStatic) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotify) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGenericNotifyFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskGenericNotifyFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskGetTickCountFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskIncrementTick) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyStateClear) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskNotifyWait) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityDisinherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityDisinherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskPriorityInherit) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskPriorityInherit) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskRemoveFromEventList) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeAll) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for .bss
    tasks.o(i.xTaskResumeFromISR) refers to port.o(i.vPortValidateInterruptPriority) for vPortValidateInterruptPriority
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for .data
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for .bss
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to list.o(i.vListInitialise) for vListInitialise
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    timers.o(i.prvCheckForValidListAndQueue) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    timers.o(i.prvCheckForValidListAndQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.data) for .data
    timers.o(i.prvCheckForValidListAndQueue) refers to timers.o(.bss) for .bss
    timers.o(i.prvInitialiseNewTimer) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.prvInitialiseNewTimer) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    timers.o(i.prvInsertTimerInActiveList) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvInsertTimerInActiveList) refers to timers.o(.data) for .data
    timers.o(i.prvProcessReceivedCommands) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessReceivedCommands) refers to heap_4.o(i.vPortFree) for vPortFree
    timers.o(i.prvProcessReceivedCommands) refers to queue.o(i.xQueueReceive) for xQueueReceive
    timers.o(i.prvProcessReceivedCommands) refers to timers.o(.data) for .data
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvSampleTimeNow) for prvSampleTimeNow
    timers.o(i.prvProcessTimerOrBlockTask) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    timers.o(i.prvProcessTimerOrBlockTask) refers to queue.o(i.vQueueWaitForMessageRestricted) for vQueueWaitForMessageRestricted
    timers.o(i.prvProcessTimerOrBlockTask) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.prvInsertTimerInActiveList) for prvInsertTimerInActiveList
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvProcessTimerOrBlockTask) refers to timers.o(.data) for .data
    timers.o(i.prvSampleTimeNow) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    timers.o(i.prvSampleTimeNow) refers to timers.o(i.prvSwitchTimerLists) for prvSwitchTimerLists
    timers.o(i.prvSampleTimeNow) refers to timers.o(.data) for .data
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.uxListRemove) for uxListRemove
    timers.o(i.prvSwitchTimerLists) refers to list.o(i.vListInsert) for vListInsert
    timers.o(i.prvSwitchTimerLists) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    timers.o(i.prvSwitchTimerLists) refers to timers.o(.data) for .data
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessTimerOrBlockTask) for prvProcessTimerOrBlockTask
    timers.o(i.prvTimerTask) refers to timers.o(i.prvProcessReceivedCommands) for prvProcessReceivedCommands
    timers.o(i.prvTimerTask) refers to timers.o(.data) for .data
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.pvTimerGetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.uxTimerGetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetReloadMode) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.vTimerSetTimerID) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    timers.o(i.xTimerCreate) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateStatic) refers to timers.o(i.prvInitialiseNewTimer) for prvInitialiseNewTimer
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvCheckForValidListAndQueue) for prvCheckForValidListAndQueue
    timers.o(i.xTimerCreateTimerTask) refers to cmsis_os2.o(i.vApplicationGetTimerTaskMemory) for vApplicationGetTimerTaskMemory
    timers.o(i.xTimerCreateTimerTask) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(.data) for .data
    timers.o(i.xTimerCreateTimerTask) refers to timers.o(i.prvTimerTask) for prvTimerTask
    timers.o(i.xTimerGenericCommand) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerGenericCommand) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerGenericCommand) refers to timers.o(.data) for .data
    timers.o(i.xTimerGetTimerDaemonTaskHandle) refers to timers.o(.data) for .data
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    timers.o(i.xTimerIsTimerActive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    timers.o(i.xTimerPendFunctionCall) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    timers.o(i.xTimerPendFunctionCall) refers to timers.o(.data) for .data
    timers.o(i.xTimerPendFunctionCallFromISR) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    timers.o(i.xTimerPendFunctionCallFromISR) refers to timers.o(.data) for .data
    cmsis_os2.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    cmsis_os2.o(i.TimerCallback) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osDelay) refers to tasks.o(i.vTaskDelay) for vTaskDelay
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osDelayUntil) refers to tasks.o(i.vTaskDelayUntil) for vTaskDelayUntil
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBitsFromISR) for xEventGroupClearBitsFromISR
    cmsis_os2.o(i.osEventFlagsClear) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsDelete) refers to event_groups.o(i.vEventGroupDelete) for vEventGroupDelete
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupGetBitsFromISR) for xEventGroupGetBitsFromISR
    cmsis_os2.o(i.osEventFlagsGet) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreateStatic) for xEventGroupCreateStatic
    cmsis_os2.o(i.osEventFlagsNew) refers to event_groups.o(i.xEventGroupCreate) for xEventGroupCreate
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBitsFromISR) for xEventGroupSetBitsFromISR
    cmsis_os2.o(i.osEventFlagsSet) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    cmsis_os2.o(i.osEventFlagsWait) refers to event_groups.o(i.xEventGroupWaitBits) for xEventGroupWaitBits
    cmsis_os2.o(i.osKernelGetInfo) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    cmsis_os2.o(i.osKernelGetState) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelGetState) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelGetSysTimerCount) refers to cmsis_os2.o(i.OS_Tick_GetCount) for OS_Tick_GetCount
    cmsis_os2.o(i.osKernelGetSysTimerFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCountFromISR) for xTaskGetTickCountFromISR
    cmsis_os2.o(i.osKernelGetTickCount) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osKernelInitialize) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osKernelRestoreLock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osKernelStart) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    cmsis_os2.o(i.osKernelStart) refers to cmsis_os2.o(.data) for .data
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    cmsis_os2.o(i.osKernelUnlock) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.AllocBlock) for AllocBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to cmsis_os2.o(i.CreateBlock) for CreateBlock
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolAlloc) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolDelete) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMemoryPoolDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    cmsis_os2.o(i.osMemoryPoolFree) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMemoryPoolGetSpace) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osMemoryPoolNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osMemoryPoolNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMessageQueueDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osMessageQueueGet) refers to queue.o(i.xQueueReceive) for xQueueReceive
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osMessageQueueGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osMessageQueueGetSpace) refers to queue.o(i.uxQueueSpacesAvailable) for uxQueueSpacesAvailable
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osMessageQueueNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSendFromISR) for xQueueGenericSendFromISR
    cmsis_os2.o(i.osMessageQueuePut) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osMessageQueueReset) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueTakeMutexRecursive) for xQueueTakeMutexRecursive
    cmsis_os2.o(i.osMutexAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osMutexDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osMutexGetOwner) refers to queue.o(i.xQueueGetMutexHolder) for xQueueGetMutexHolder
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutexStatic) for xQueueCreateMutexStatic
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.xQueueCreateMutex) for xQueueCreateMutex
    cmsis_os2.o(i.osMutexNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGiveMutexRecursive) for xQueueGiveMutexRecursive
    cmsis_os2.o(i.osMutexRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueReceiveFromISR) for xQueueReceiveFromISR
    cmsis_os2.o(i.osSemaphoreAcquire) refers to queue.o(i.xQueueSemaphoreTake) for xQueueSemaphoreTake
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueUnregisterQueue) for vQueueUnregisterQueue
    cmsis_os2.o(i.osSemaphoreDelete) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaitingFromISR) for uxQueueMessagesWaitingFromISR
    cmsis_os2.o(i.osSemaphoreGetCount) refers to queue.o(i.uxQueueMessagesWaiting) for uxQueueMessagesWaiting
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphoreStatic) for xQueueCreateCountingSemaphoreStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreateStatic) for xQueueGenericCreateStatic
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericCreate) for xQueueGenericCreate
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueDelete) for vQueueDelete
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.xQueueCreateCountingSemaphore) for xQueueCreateCountingSemaphore
    cmsis_os2.o(i.osSemaphoreNew) refers to queue.o(i.vQueueAddToRegistry) for vQueueAddToRegistry
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGiveFromISR) for xQueueGiveFromISR
    cmsis_os2.o(i.osSemaphoreRelease) refers to queue.o(i.xQueueGenericSend) for xQueueGenericSend
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.uxTaskGetSystemState) for uxTaskGetSystemState
    cmsis_os2.o(i.osThreadEnumerate) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    cmsis_os2.o(i.osThreadEnumerate) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osThreadExit) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsClear) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadFlagsGet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    cmsis_os2.o(i.osThreadFlagsSet) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    cmsis_os2.o(i.osThreadFlagsWait) refers to tasks.o(i.xTaskNotifyWait) for xTaskNotifyWait
    cmsis_os2.o(i.osThreadGetCount) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    cmsis_os2.o(i.osThreadGetId) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    cmsis_os2.o(i.osThreadGetName) refers to tasks.o(i.pcTaskGetName) for pcTaskGetName
    cmsis_os2.o(i.osThreadGetPriority) refers to tasks.o(i.uxTaskPriorityGet) for uxTaskPriorityGet
    cmsis_os2.o(i.osThreadGetStackSpace) refers to tasks.o(i.uxTaskGetStackHighWaterMark) for uxTaskGetStackHighWaterMark
    cmsis_os2.o(i.osThreadGetState) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreateStatic) for xTaskCreateStatic
    cmsis_os2.o(i.osThreadNew) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    cmsis_os2.o(i.osThreadResume) refers to tasks.o(i.vTaskResume) for vTaskResume
    cmsis_os2.o(i.osThreadSetPriority) refers to tasks.o(i.vTaskPrioritySet) for vTaskPrioritySet
    cmsis_os2.o(i.osThreadSuspend) refers to tasks.o(i.vTaskSuspend) for vTaskSuspend
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.eTaskGetState) for eTaskGetState
    cmsis_os2.o(i.osThreadTerminate) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.pvTimerGetTimerID) for pvTimerGetTimerID
    cmsis_os2.o(i.osTimerDelete) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerGetName) refers to timers.o(i.pcTimerGetName) for pcTimerGetName
    cmsis_os2.o(i.osTimerIsRunning) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreateStatic) for xTimerCreateStatic
    cmsis_os2.o(i.osTimerNew) refers to timers.o(i.xTimerCreate) for xTimerCreate
    cmsis_os2.o(i.osTimerNew) refers to heap_4.o(i.vPortFree) for vPortFree
    cmsis_os2.o(i.osTimerNew) refers to cmsis_os2.o(i.TimerCallback) for TimerCallback
    cmsis_os2.o(i.osTimerStart) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerIsTimerActive) for xTimerIsTimerActive
    cmsis_os2.o(i.osTimerStop) refers to timers.o(i.xTimerGenericCommand) for xTimerGenericCommand
    cmsis_os2.o(i.vApplicationGetIdleTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    cmsis_os2.o(i.vApplicationGetTimerTaskMemory) refers to cmsis_os2.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for .bss
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for .data
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for .data
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for .data
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for .data
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for .data
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.prvTaskExitError) refers to port.o(.data) for .data
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEndScheduler) refers to port.o(.data) for .data
    port.o(i.vPortEnterCritical) refers to port.o(.data) for .data
    port.o(i.vPortExitCritical) refers to port.o(.data) for .data
    port.o(i.vPortSetupTimerInterrupt) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.emb_text) for vPortGetIPSR
    port.o(i.vPortValidateInterruptPriority) refers to port.o(.data) for .data
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvStartFirstTask
    port.o(i.xPortStartScheduler) refers to port.o(.data) for .data
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    memset.o(.text) refers to rt_memclr.o(.text) for _memset
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_DrawArc), (586 bytes).
    Removing oled.o(i.OLED_DrawCircle), (318 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (692 bytes).
    Removing oled.o(i.OLED_DrawLine), (234 bytes).
    Removing oled.o(i.OLED_DrawPoint), (52 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (116 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (196 bytes).
    Removing oled.o(i.OLED_GetPoint), (56 bytes).
    Removing oled.o(i.OLED_I2C_SendByte), (2 bytes).
    Removing oled.o(i.OLED_I2C_Start), (2 bytes).
    Removing oled.o(i.OLED_I2C_Stop), (2 bytes).
    Removing oled.o(i.OLED_IsInAngle), (96 bytes).
    Removing oled.o(i.OLED_Pow), (14 bytes).
    Removing oled.o(i.OLED_Printf), (42 bytes).
    Removing oled.o(i.OLED_Reverse), (40 bytes).
    Removing oled.o(i.OLED_ReverseArea), (88 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (64 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (190 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (72 bytes).
    Removing oled.o(i.OLED_ShowNum), (70 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (94 bytes).
    Removing oled.o(i.OLED_UpdateArea), (96 bytes).
    Removing oled.o(i.OLED_W_SCL), (2 bytes).
    Removing oled.o(i.OLED_W_SDA), (2 bytes).
    Removing oled.o(i.OLED_pnpoly), (116 bytes).
    Removing oled_data.o(.constdata), (32 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing oled_data.o(.constdata), (1024 bytes).
    Removing guitar.o(.rev16_text), (4 bytes).
    Removing guitar.o(.revsh_text), (4 bytes).
    Removing guitar.o(.rrx_text), (6 bytes).
    Removing guitar.o(i.Guitar_DebugFrequencies), (36 bytes).
    Removing guitar.o(i.Guitar_FastSwitchFrequency), (4 bytes).
    Removing guitar.o(i.Guitar_PlayCalibrationSequence), (44 bytes).
    Removing guitar.o(i.Guitar_PlayNote), (32 bytes).
    Removing guitar.o(i.Guitar_PlayTuningA4), (12 bytes).
    Removing guitar.o(i.Guitar_ProcessKeyEvent), (84 bytes).
    Removing guitar.o(i.Guitar_QuickBuzzerTest), (4 bytes).
    Removing guitar.o(i.Guitar_SetPreciseFrequency), (144 bytes).
    Removing guitar.o(i.Guitar_SimpleScaleTest), (72 bytes).
    Removing guitar.o(i.Guitar_TestA4Accuracy), (84 bytes).
    Removing guitar.o(i.Guitar_TestIntervalAccuracy), (2 bytes).
    Removing guitar.o(i.Guitar_TestOLED), (152 bytes).
    Removing guitar.o(i.Guitar_VerifyTimerConfig), (40 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing freertos.o(.rev16_text), (4 bytes).
    Removing freertos.o(.revsh_text), (4 bytes).
    Removing freertos.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (56 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_ResumeTick), (20 bytes).
    Removing stm32f1xx_hal_timebase_tim.o(i.HAL_SuspendTick), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start), (80 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (152 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start), (184 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (416 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (228 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start), (156 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (104 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (124 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (100 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (404 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (200 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (112 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (196 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (176 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI1_SetConfig), (88 bytes).
    Removing stm32f1xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (184 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (140 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (152 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (352 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (192 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_InitTick), (64 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (44 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (164 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (280 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort), (70 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT), (152 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (92 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (532 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config), (40 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (260 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (592 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (604 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (452 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (400 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (228 bytes).
    Removing stm32f1xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAError), (54 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_ADDR), (344 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (20 bytes).
    Removing event_groups.o(i.uxEventGroupGetNumber), (8 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (74 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (4 bytes).
    Removing event_groups.o(i.vEventGroupSetNumber), (4 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (64 bytes).
    Removing event_groups.o(i.xEventGroupClearBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (28 bytes).
    Removing event_groups.o(i.xEventGroupCreateStatic), (44 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (26 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (140 bytes).
    Removing event_groups.o(i.xEventGroupSetBitsFromISR), (16 bytes).
    Removing event_groups.o(i.xEventGroupSync), (204 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (264 bytes).
    Removing queue.o(i.pcQueueGetName), (40 bytes).
    Removing queue.o(i.ucQueueGetQueueType), (6 bytes).
    Removing queue.o(i.uxQueueGetQueueNumber), (4 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (36 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (22 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (40 bytes).
    Removing queue.o(i.vQueueDelete), (46 bytes).
    Removing queue.o(i.vQueueSetQueueNumber), (4 bytes).
    Removing queue.o(i.vQueueUnregisterQueue), (40 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphore), (58 bytes).
    Removing queue.o(i.xQueueCreateCountingSemaphoreStatic), (64 bytes).
    Removing queue.o(i.xQueueGetMutexHolder), (28 bytes).
    Removing queue.o(i.xQueueGetMutexHolderFromISR), (30 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (156 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (30 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (34 bytes).
    Removing queue.o(i.xQueuePeek), (308 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (116 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (18 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (66 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (140 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (58 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (130 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (60 bytes).
    Removing stream_buffer.o(i.ucStreamBufferGetStreamBufferType), (8 bytes).
    Removing stream_buffer.o(i.uxStreamBufferGetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (34 bytes).
    Removing stream_buffer.o(i.vStreamBufferSetStreamBufferNumber), (4 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (108 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreateStatic), (122 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (34 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (48 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (78 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (212 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (144 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (258 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (70 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (142 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (40 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (38 bytes).
    Removing tasks.o(i.eTaskGetState), (116 bytes).
    Removing tasks.o(i.pcTaskGetName), (32 bytes).
    Removing tasks.o(i.prvListTasksWithinSingleList), (88 bytes).
    Removing tasks.o(i.prvTaskCheckFreeStackSpace), (20 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (52 bytes).
    Removing tasks.o(i.ulTaskNotifyTake), (104 bytes).
    Removing tasks.o(i.ulTaskNotifyValueClear), (44 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskGetStackHighWaterMark), (16 bytes).
    Removing tasks.o(i.uxTaskGetSystemState), (172 bytes).
    Removing tasks.o(i.uxTaskGetTaskNumber), (8 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (28 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (44 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (24 bytes).
    Removing tasks.o(i.vTaskDelayUntil), (140 bytes).
    Removing tasks.o(i.vTaskDelete), (144 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (28 bytes).
    Removing tasks.o(i.vTaskGetInfo), (116 bytes).
    Removing tasks.o(i.vTaskNotifyGiveFromISR), (176 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (80 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (180 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (112 bytes).
    Removing tasks.o(i.vTaskResume), (124 bytes).
    Removing tasks.o(i.vTaskSetTaskNumber), (8 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (48 bytes).
    Removing tasks.o(i.vTaskSuspend), (156 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (48 bytes).
    Removing tasks.o(i.xTaskGenericNotify), (224 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (260 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (16 bytes).
    Removing tasks.o(i.xTaskNotifyStateClear), (52 bytes).
    Removing tasks.o(i.xTaskNotifyWait), (140 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (136 bytes).
    Removing timers.o(i.pcTimerGetName), (22 bytes).
    Removing timers.o(i.prvInitialiseNewTimer), (78 bytes).
    Removing timers.o(i.pvTimerGetTimerID), (36 bytes).
    Removing timers.o(i.uxTimerGetReloadMode), (48 bytes).
    Removing timers.o(i.uxTimerGetTimerNumber), (4 bytes).
    Removing timers.o(i.vTimerSetReloadMode), (54 bytes).
    Removing timers.o(i.vTimerSetTimerID), (38 bytes).
    Removing timers.o(i.vTimerSetTimerNumber), (4 bytes).
    Removing timers.o(i.xTimerCreate), (52 bytes).
    Removing timers.o(i.xTimerCreateStatic), (46 bytes).
    Removing timers.o(i.xTimerGetExpiryTime), (22 bytes).
    Removing timers.o(i.xTimerGetPeriod), (22 bytes).
    Removing timers.o(i.xTimerGetTimerDaemonTaskHandle), (32 bytes).
    Removing timers.o(i.xTimerIsTimerActive), (48 bytes).
    Removing timers.o(i.xTimerPendFunctionCall), (60 bytes).
    Removing timers.o(i.xTimerPendFunctionCallFromISR), (40 bytes).
    Removing cmsis_os2.o(.rev16_text), (4 bytes).
    Removing cmsis_os2.o(.revsh_text), (4 bytes).
    Removing cmsis_os2.o(.rrx_text), (6 bytes).
    Removing cmsis_os2.o(i.AllocBlock), (18 bytes).
    Removing cmsis_os2.o(i.CreateBlock), (26 bytes).
    Removing cmsis_os2.o(i.OS_Tick_GetCount), (12 bytes).
    Removing cmsis_os2.o(i.TimerCallback), (24 bytes).
    Removing cmsis_os2.o(i.osDelayUntil), (46 bytes).
    Removing cmsis_os2.o(i.osEventFlagsClear), (62 bytes).
    Removing cmsis_os2.o(i.osEventFlagsDelete), (32 bytes).
    Removing cmsis_os2.o(i.osEventFlagsGet), (22 bytes).
    Removing cmsis_os2.o(i.osEventFlagsNew), (44 bytes).
    Removing cmsis_os2.o(i.osEventFlagsSet), (84 bytes).
    Removing cmsis_os2.o(i.osEventFlagsWait), (98 bytes).
    Removing cmsis_os2.o(i.osKernelGetInfo), (60 bytes).
    Removing cmsis_os2.o(i.osKernelGetState), (32 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerCount), (66 bytes).
    Removing cmsis_os2.o(i.osKernelGetSysTimerFreq), (12 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickCount), (14 bytes).
    Removing cmsis_os2.o(i.osKernelGetTickFreq), (6 bytes).
    Removing cmsis_os2.o(i.osKernelLock), (42 bytes).
    Removing cmsis_os2.o(i.osKernelRestoreLock), (66 bytes).
    Removing cmsis_os2.o(i.osKernelUnlock), (58 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolAlloc), (144 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolDelete), (94 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolFree), (180 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetBlockSize), (24 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCapacity), (24 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetCount), (52 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetName), (18 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolGetSpace), (40 bytes).
    Removing cmsis_os2.o(i.osMemoryPoolNew), (228 bytes).
    Removing cmsis_os2.o(i.osMessageQueueDelete), (42 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCapacity), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetCount), (20 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetMsgSize), (8 bytes).
    Removing cmsis_os2.o(i.osMessageQueueGetSpace), (44 bytes).
    Removing cmsis_os2.o(i.osMessageQueueReset), (34 bytes).
    Removing cmsis_os2.o(i.osMutexDelete), (44 bytes).
    Removing cmsis_os2.o(i.osMutexGetOwner), (20 bytes).
    Removing cmsis_os2.o(i.osSemaphoreAcquire), (92 bytes).
    Removing cmsis_os2.o(i.osSemaphoreDelete), (42 bytes).
    Removing cmsis_os2.o(i.osSemaphoreGetCount), (20 bytes).
    Removing cmsis_os2.o(i.osSemaphoreNew), (152 bytes).
    Removing cmsis_os2.o(i.osSemaphoreRelease), (84 bytes).
    Removing cmsis_os2.o(i.osThreadEnumerate), (98 bytes).
    Removing cmsis_os2.o(i.osThreadExit), (8 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsClear), (78 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsGet), (44 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsSet), (116 bytes).
    Removing cmsis_os2.o(i.osThreadFlagsWait), (148 bytes).
    Removing cmsis_os2.o(i.osThreadGetCount), (14 bytes).
    Removing cmsis_os2.o(i.osThreadGetId), (4 bytes).
    Removing cmsis_os2.o(i.osThreadGetName), (16 bytes).
    Removing cmsis_os2.o(i.osThreadGetPriority), (18 bytes).
    Removing cmsis_os2.o(i.osThreadGetStackSpace), (22 bytes).
    Removing cmsis_os2.o(i.osThreadGetState), (52 bytes).
    Removing cmsis_os2.o(i.osThreadResume), (32 bytes).
    Removing cmsis_os2.o(i.osThreadSetPriority), (40 bytes).
    Removing cmsis_os2.o(i.osThreadSuspend), (32 bytes).
    Removing cmsis_os2.o(i.osThreadTerminate), (52 bytes).
    Removing cmsis_os2.o(i.osThreadYield), (36 bytes).
    Removing cmsis_os2.o(i.osTimerDelete), (68 bytes).
    Removing cmsis_os2.o(i.osTimerGetName), (16 bytes).
    Removing cmsis_os2.o(i.osTimerIsRunning), (16 bytes).
    Removing cmsis_os2.o(i.osTimerNew), (120 bytes).
    Removing cmsis_os2.o(i.osTimerStart), (50 bytes).
    Removing cmsis_os2.o(i.osTimerStop), (68 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (108 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (32 bytes).

623 unused section(s) (total 55604 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/croutine.c 0x00000000   Number         0  croutine.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c 0x00000000   Number         0  event_groups.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/list.c 0x00000000   Number         0  list.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c 0x00000000   Number         0  heap_4.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3/port.c 0x00000000   Number         0  port.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/queue.c 0x00000000   Number         0  queue.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c 0x00000000   Number         0  stream_buffer.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/tasks.c 0x00000000   Number         0  tasks.o ABSOLUTE
    ../Middlewares/Third_Party/FreeRTOS/Source/timers.c 0x00000000   Number         0  timers.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcat.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ..\Core\Src\OLED.c                       0x00000000   Number         0  oled.o ABSOLUTE
    ..\Core\Src\OLED_Data.c                  0x00000000   Number         0  oled_data.o ABSOLUTE
    ..\Core\Src\freertos.c                   0x00000000   Number         0  freertos.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\guitar.c                     0x00000000   Number         0  guitar.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_timebase_tim.c 0x00000000   Number         0  stm32f1xx_hal_timebase_tim.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_i2c.c 0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\CMSIS_RTOS_V2\cmsis_os2.c 0x00000000   Number         0  cmsis_os2.o ABSOLUTE
    ..\Middlewares\Third_Party\FreeRTOS\Source\portable\RVDS\ARM_CM3\port.c 0x00000000   Number         0  port.o ABSOLUTE
    ..\\Core\\Src\\OLED.c                    0x00000000   Number         0  oled.o ABSOLUTE
    ..\\Core\\Src\\guitar.c                  0x00000000   Number         0  guitar.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x08000160   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000009  0x08000160   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000166   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800016a   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800016c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800016c   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x0800016e   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x08000170   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x08000170   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000170   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x08000170   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x08000170   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x08000170   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x08000172   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000172   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000178   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000178   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800017c   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800017c   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000184   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000186   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000186   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800018a   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000190   Section      150  port.o(.emb_text)
    .text                                    0x08000228   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x08000268   Section        0  __2sprintf.o(.text)
    .text                                    0x08000294   Section        0  _printf_dec.o(.text)
    .text                                    0x0800030c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000494   Section        0  strcpy.o(.text)
    .text                                    0x080004dc   Section        0  strlen.o(.text)
    .text                                    0x0800051a   Section        0  strcat.o(.text)
    .text                                    0x08000532   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080005bc   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000620   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000630   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800067e   Section        0  heapauxi.o(.text)
    .text                                    0x08000684   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000738   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000739   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000768   Section        0  _sputc.o(.text)
    .text                                    0x08000772   Section       68  rt_memclr.o(.text)
    .text                                    0x080007b8   Section        8  libspace.o(.text)
    .text                                    0x080007c0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800080a   Section        0  exit.o(.text)
    .text                                    0x08000818   Section        0  sys_exit.o(.text)
    .text                                    0x08000824   Section        2  use_no_semi.o(.text)
    .text                                    0x08000826   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x08000826   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x08000828   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x0800082a   Section        0  main.o(i.Error_Handler)
    i.Guitar_CheckKeyCombo                   0x08000830   Section        0  guitar.o(i.Guitar_CheckKeyCombo)
    i.Guitar_GetNoteFrequency                0x080008c0   Section        0  guitar.o(i.Guitar_GetNoteFrequency)
    i.Guitar_GetPreciseNoteFrequency         0x080008d8   Section        0  guitar.o(i.Guitar_GetPreciseNoteFrequency)
    i.Guitar_Init                            0x08000924   Section        0  guitar.o(i.Guitar_Init)
    i.Guitar_IsKeyPressed                    0x0800095c   Section        0  guitar.o(i.Guitar_IsKeyPressed)
    i.Guitar_PlaySimpleChord                 0x08000998   Section        0  guitar.o(i.Guitar_PlaySimpleChord)
    i.Guitar_PrecomputePeriods               0x08000a24   Section        0  guitar.o(i.Guitar_PrecomputePeriods)
    i.Guitar_ScanKeys                        0x08000ae8   Section        0  guitar.o(i.Guitar_ScanKeys)
    i.Guitar_SetFrequency                    0x08000b98   Section        0  guitar.o(i.Guitar_SetFrequency)
    i.Guitar_SetVolume                       0x08000be0   Section        0  guitar.o(i.Guitar_SetVolume)
    i.Guitar_SimpleMetronome                 0x08000c0c   Section        0  guitar.o(i.Guitar_SimpleMetronome)
    i.Guitar_SimpleVibrato                   0x08000c74   Section        0  guitar.o(i.Guitar_SimpleVibrato)
    i.Guitar_StopNote                        0x08000cb8   Section        0  guitar.o(i.Guitar_StopNote)
    i.Guitar_UltraFastSwitchNote             0x08000cd0   Section        0  guitar.o(i.Guitar_UltraFastSwitchNote)
    i.Guitar_UpdateDisplay                   0x08000d2c   Section        0  guitar.o(i.Guitar_UpdateDisplay)
    i.HAL_Delay                              0x08000edc   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08000f00   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080010e0   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_TogglePin                     0x080010ea   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    i.HAL_GPIO_WritePin                      0x080010fa   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001104   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08001110   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_IsDeviceReady                  0x08001298   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady)
    i.HAL_I2C_Master_Transmit                0x08001404   Section        0  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MspInit                        0x08001530   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x0800158c   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x0800159c   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080015c0   Section        0  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08001658   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080016a0   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x080016bc   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x080016fc   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08001720   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetClockConfig                 0x0800184c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x0800188c   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080018ac   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080018cc   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08001918   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_TIMEx_BreakCallback                0x08001c38   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x08001c3a   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08001c3c   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08001c90   Section        0  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08001cf8   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08001d54   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08001d7c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08001dd4   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_IC_CaptureCallback             0x08001eb0   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08001eb2   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08001fe4   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08002030   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08002032   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080020fe   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08002158   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x0800215a   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x0800215c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x080021f8   Section        0  main.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x0800220c   Section        0  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HardFault_Handler                      0x0800220e   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08002210   Section        0  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08002211   Thumb Code    46  stm32f1xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterRequestWrite                 0x08002240   Section        0  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x08002241   Thumb Code   150  stm32f1xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x080022dc   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x080022dd   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08002334   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08002335   Thumb Code   144  stm32f1xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x080023c4   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x080023c5   Thumb Code   188  stm32f1xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08002480   Section        0  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08002481   Thumb Code    86  stm32f1xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.MX_FREERTOS_Init                       0x080024d8   Section        0  freertos.o(i.MX_FREERTOS_Init)
    i.MX_GPIO_Init                           0x08002540   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x080025d4   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_TIM1_Init                           0x08002614   Section        0  tim.o(i.MX_TIM1_Init)
    i.MemManage_Handler                      0x080026e0   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080026e2   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.OLED_CheckBufferIntegrity              0x080026e4   Section        0  oled.o(i.OLED_CheckBufferIntegrity)
    i.OLED_Clear                             0x080026fc   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x08002724   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_Init                              0x08002780   Section        0  oled.o(i.OLED_Init)
    i.OLED_PowerOnInit                       0x08002838   Section        0  oled.o(i.OLED_PowerOnInit)
    i.OLED_SetCursor                         0x08002884   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x080028a8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x080028e4   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x080029a0   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x080029cc   Section        0  oled.o(i.OLED_Update)
    i.OLED_WriteCommand                      0x08002a34   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x08002a60   Section        0  oled.o(i.OLED_WriteData)
    i.StartAudioTask                         0x08002aa0   Section        0  freertos.o(i.StartAudioTask)
    i.StartDisplayTask                       0x08002af0   Section        0  freertos.o(i.StartDisplayTask)
    i.StartKeyScanTask                       0x08002bc4   Section        0  freertos.o(i.StartKeyScanTask)
    i.SysTick_Handler                        0x08002bd0   Section        0  cmsis_os2.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08002bea   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08002c48   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.TIM4_IRQHandler                        0x08002c4c   Section        0  stm32f1xx_it.o(i.TIM4_IRQHandler)
    i.TIM_Base_SetConfig                     0x08002c58   Section        0  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08002cd0   Section        0  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08002cea   Section        0  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08002cfe   Section        0  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08002cff   Thumb Code    16  stm32f1xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08002d10   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08002d11   Thumb Code    74  stm32f1xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08002d60   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08002db8   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08002db9   Thumb Code    82  stm32f1xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08002e10   Section        0  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08002e11   Thumb Code    64  stm32f1xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08002e54   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08002e55   Thumb Code    34  stm32f1xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08002e76   Section        0  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08002e77   Thumb Code    36  stm32f1xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UsageFault_Handler                     0x08002e9a   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x08002e9c   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002e9d   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i._is_digit                              0x08002ebc   Section        0  __printf_wp.o(i._is_digit)
    i.main                                   0x08002eca   Section        0  main.o(i.main)
    i.osDelay                                0x08002ef0   Section        0  cmsis_os2.o(i.osDelay)
    i.osKernelInitialize                     0x08002f0c   Section        0  cmsis_os2.o(i.osKernelInitialize)
    i.osKernelStart                          0x08002f30   Section        0  cmsis_os2.o(i.osKernelStart)
    i.osMessageQueueGet                      0x08002f68   Section        0  cmsis_os2.o(i.osMessageQueueGet)
    i.osMessageQueueNew                      0x08002fcc   Section        0  cmsis_os2.o(i.osMessageQueueNew)
    i.osMessageQueuePut                      0x08003024   Section        0  cmsis_os2.o(i.osMessageQueuePut)
    i.osMutexAcquire                         0x0800308c   Section        0  cmsis_os2.o(i.osMutexAcquire)
    i.osMutexNew                             0x080030de   Section        0  cmsis_os2.o(i.osMutexNew)
    i.osMutexRelease                         0x08003146   Section        0  cmsis_os2.o(i.osMutexRelease)
    i.osThreadNew                            0x08003188   Section        0  cmsis_os2.o(i.osThreadNew)
    i.prvAddCurrentTaskToDelayedList         0x08003210   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08003211   Thumb Code    84  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x0800326c   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x0800326d   Thumb Code   190  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckForValidListAndQueue           0x0800333c   Section        0  timers.o(i.prvCheckForValidListAndQueue)
    prvCheckForValidListAndQueue             0x0800333d   Thumb Code    72  timers.o(i.prvCheckForValidListAndQueue)
    i.prvCopyDataFromQueue                   0x08003394   Section        0  queue.o(i.prvCopyDataFromQueue)
    prvCopyDataFromQueue                     0x08003395   Thumb Code    38  queue.o(i.prvCopyDataFromQueue)
    i.prvCopyDataToQueue                     0x080033ba   Section        0  queue.o(i.prvCopyDataToQueue)
    prvCopyDataToQueue                       0x080033bb   Thumb Code   108  queue.o(i.prvCopyDataToQueue)
    i.prvDeleteTCB                           0x08003426   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08003427   Thumb Code    52  tasks.o(i.prvDeleteTCB)
    i.prvHeapInit                            0x0800345c   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x0800345d   Thumb Code    64  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x080034a4   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x080034a5   Thumb Code    82  tasks.o(i.prvIdleTask)
    i.prvInitialiseMutex                     0x08003504   Section        0  queue.o(i.prvInitialiseMutex)
    prvInitialiseMutex                       0x08003505   Thumb Code    22  queue.o(i.prvInitialiseMutex)
    i.prvInitialiseNewQueue                  0x0800351c   Section        0  queue.o(i.prvInitialiseNewQueue)
    prvInitialiseNewQueue                    0x0800351d   Thumb Code    34  queue.o(i.prvInitialiseNewQueue)
    i.prvInitialiseNewTask                   0x08003540   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08003541   Thumb Code   176  tasks.o(i.prvInitialiseNewTask)
    i.prvInsertBlockIntoFreeList             0x080035f0   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x080035f1   Thumb Code    72  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvInsertTimerInActiveList             0x0800363c   Section        0  timers.o(i.prvInsertTimerInActiveList)
    prvInsertTimerInActiveList               0x0800363d   Thumb Code    52  timers.o(i.prvInsertTimerInActiveList)
    i.prvIsQueueEmpty                        0x08003674   Section        0  queue.o(i.prvIsQueueEmpty)
    prvIsQueueEmpty                          0x08003675   Thumb Code    28  queue.o(i.prvIsQueueEmpty)
    i.prvProcessReceivedCommands             0x08003690   Section        0  timers.o(i.prvProcessReceivedCommands)
    prvProcessReceivedCommands               0x08003691   Thumb Code   248  timers.o(i.prvProcessReceivedCommands)
    i.prvProcessTimerOrBlockTask             0x0800378c   Section        0  timers.o(i.prvProcessTimerOrBlockTask)
    prvProcessTimerOrBlockTask               0x0800378d   Thumb Code   182  timers.o(i.prvProcessTimerOrBlockTask)
    i.prvResetNextTaskUnblockTime            0x0800384c   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x0800384d   Thumb Code    26  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvSampleTimeNow                       0x0800386c   Section        0  timers.o(i.prvSampleTimeNow)
    prvSampleTimeNow                         0x0800386d   Thumb Code    36  timers.o(i.prvSampleTimeNow)
    i.prvSwitchTimerLists                    0x08003894   Section        0  timers.o(i.prvSwitchTimerLists)
    prvSwitchTimerLists                      0x08003895   Thumb Code   104  timers.o(i.prvSwitchTimerLists)
    i.prvTaskExitError                       0x08003900   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x08003901   Thumb Code    36  port.o(i.prvTaskExitError)
    i.prvTimerTask                           0x08003928   Section        0  timers.o(i.prvTimerTask)
    prvTimerTask                             0x08003929   Thumb Code    32  timers.o(i.prvTimerTask)
    i.prvUnlockQueue                         0x0800394c   Section        0  queue.o(i.prvUnlockQueue)
    prvUnlockQueue                           0x0800394d   Thumb Code   106  queue.o(i.prvUnlockQueue)
    i.pvPortMalloc                           0x080039b8   Section        0  heap_4.o(i.pvPortMalloc)
    i.pvTaskIncrementMutexHeldCount          0x08003a94   Section        0  tasks.o(i.pvTaskIncrementMutexHeldCount)
    i.pxPortInitialiseStack                  0x08003aac   Section        0  port.o(i.pxPortInitialiseStack)
    i.uxListRemove                           0x08003ad0   Section        0  list.o(i.uxListRemove)
    i.vApplicationGetIdleTaskMemory          0x08003af8   Section        0  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    i.vApplicationGetTimerTaskMemory         0x08003b0c   Section        0  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    i.vListInitialise                        0x08003b24   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08003b3a   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08003b40   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08003b70   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08003b88   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08003bc8   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08003bf0   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x08003c54   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vPortValidateInterruptPriority         0x08003c78   Section        0  port.o(i.vPortValidateInterruptPriority)
    i.vQueueAddToRegistry                    0x08003ccc   Section        0  queue.o(i.vQueueAddToRegistry)
    i.vQueueWaitForMessageRestricted         0x08003cf4   Section        0  queue.o(i.vQueueWaitForMessageRestricted)
    i.vTaskDelay                             0x08003d38   Section        0  tasks.o(i.vTaskDelay)
    i.vTaskInternalSetTimeOutState           0x08003d84   Section        0  tasks.o(i.vTaskInternalSetTimeOutState)
    i.vTaskMissedYield                       0x08003d94   Section        0  tasks.o(i.vTaskMissedYield)
    i.vTaskPlaceOnEventList                  0x08003da0   Section        0  tasks.o(i.vTaskPlaceOnEventList)
    i.vTaskPlaceOnEventListRestricted        0x08003dd0   Section        0  tasks.o(i.vTaskPlaceOnEventListRestricted)
    i.vTaskPriorityDisinheritAfterTimeout    0x08003e08   Section        0  tasks.o(i.vTaskPriorityDisinheritAfterTimeout)
    i.vTaskStartScheduler                    0x08003e98   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x08003f20   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08003f30   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x08003f94   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x0800403c   Section        0  port.o(i.xPortSysTickHandler)
    i.xQueueCreateMutex                      0x08004068   Section        0  queue.o(i.xQueueCreateMutex)
    i.xQueueCreateMutexStatic                0x0800407e   Section        0  queue.o(i.xQueueCreateMutexStatic)
    i.xQueueGenericCreate                    0x08004098   Section        0  queue.o(i.xQueueGenericCreate)
    i.xQueueGenericCreateStatic              0x080040da   Section        0  queue.o(i.xQueueGenericCreateStatic)
    i.xQueueGenericReset                     0x08004140   Section        0  queue.o(i.xQueueGenericReset)
    i.xQueueGenericSend                      0x080041c8   Section        0  queue.o(i.xQueueGenericSend)
    i.xQueueGenericSendFromISR               0x08004328   Section        0  queue.o(i.xQueueGenericSendFromISR)
    i.xQueueGiveMutexRecursive               0x080043e6   Section        0  queue.o(i.xQueueGiveMutexRecursive)
    i.xQueueReceive                          0x08004424   Section        0  queue.o(i.xQueueReceive)
    i.xQueueReceiveFromISR                   0x0800455c   Section        0  queue.o(i.xQueueReceiveFromISR)
    i.xQueueSemaphoreTake                    0x080045f8   Section        0  queue.o(i.xQueueSemaphoreTake)
    i.xQueueTakeMutexRecursive               0x08004770   Section        0  queue.o(i.xQueueTakeMutexRecursive)
    i.xTaskCheckForTimeOut                   0x080047b0   Section        0  tasks.o(i.xTaskCheckForTimeOut)
    i.xTaskCreate                            0x08004824   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskCreateStatic                      0x0800487e   Section        0  tasks.o(i.xTaskCreateStatic)
    i.xTaskGetCurrentTaskHandle              0x080048d4   Section        0  tasks.o(i.xTaskGetCurrentTaskHandle)
    i.xTaskGetSchedulerState                 0x080048e0   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x080048fc   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x08004908   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskPriorityDisinherit                0x080049d0   Section        0  tasks.o(i.xTaskPriorityDisinherit)
    i.xTaskPriorityInherit                   0x08004a50   Section        0  tasks.o(i.xTaskPriorityInherit)
    i.xTaskRemoveFromEventList               0x08004ac8   Section        0  tasks.o(i.xTaskRemoveFromEventList)
    i.xTaskResumeAll                         0x08004b38   Section        0  tasks.o(i.xTaskResumeAll)
    i.xTimerCreateTimerTask                  0x08004bfc   Section        0  timers.o(i.xTimerCreateTimerTask)
    i.xTimerGenericCommand                   0x08004c5c   Section        0  timers.o(i.xTimerGenericCommand)
    x$fpl$d2f                                0x08004cc4   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08004d28   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08004d39   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08004e78   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$ddiv                               0x08004e88   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08004e8f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfixu                              0x08005138   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x08005192   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dnaninf                            0x080051b8   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08005254   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$dsub                               0x08005260   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08005271   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$fadd                               0x08005434   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08005443   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$ffixu                              0x080054f8   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fnaninf                            0x08005536   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x080055c2   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x080055cc   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x080055db   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$retnan                             0x080056b6   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800571a   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x08005776   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x080057a6   Section     1520  oled_data.o(.constdata)
    x$fpl$usenofp                            0x080057a6   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08005d96   Section      570  oled_data.o(.constdata)
    .constdata                               0x08005fd0   Section       88  guitar.o(.constdata)
    .constdata                               0x08006028   Section      172  freertos.o(.constdata)
    .constdata                               0x080060d4   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x080060d4   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x080060d6   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x080060e6   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x080060f6   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x080060fe   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x080060fe   Data          17  __printf_flags_ss_wp.o(.constdata)
    .conststring                             0x08006110   Section       55  guitar.o(.conststring)
    .conststring                             0x08006148   Section       82  freertos.o(.conststring)
    .data                                    0x20000000   Section       28  guitar.o(.data)
    keyLastState                             0x20000000   Data           6  guitar.o(.data)
    keyStableState                           0x20000006   Data           6  guitar.o(.data)
    .data                                    0x2000001c   Section       24  freertos.o(.data)
    .data                                    0x20000034   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x20000040   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000044   Section       60  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x20000048   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x2000004c   Data           4  tasks.o(.data)
    xTickCount                               0x20000050   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x20000054   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x20000058   Data           4  tasks.o(.data)
    xPendedTicks                             0x2000005c   Data           4  tasks.o(.data)
    xYieldPending                            0x20000060   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x20000064   Data           4  tasks.o(.data)
    uxTaskNumber                             0x20000068   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x2000006c   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x20000070   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000074   Data           4  tasks.o(.data)
    pxDelayedTaskList                        0x20000078   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x2000007c   Data           4  tasks.o(.data)
    .data                                    0x20000080   Section       20  timers.o(.data)
    xTimerQueue                              0x20000080   Data           4  timers.o(.data)
    xTimerTaskHandle                         0x20000084   Data           4  timers.o(.data)
    xLastTime                                0x20000088   Data           4  timers.o(.data)
    pxCurrentTimerList                       0x2000008c   Data           4  timers.o(.data)
    pxOverflowTimerList                      0x20000090   Data           4  timers.o(.data)
    .data                                    0x20000094   Section        4  cmsis_os2.o(.data)
    KernelState                              0x20000094   Data           4  cmsis_os2.o(.data)
    .data                                    0x20000098   Section       32  heap_4.o(.data)
    pxEnd                                    0x20000098   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x2000009c   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x200000a0   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x200000a4   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x200000a8   Data           4  heap_4.o(.data)
    xBlockAllocatedBit                       0x200000ac   Data           4  heap_4.o(.data)
    xStart                                   0x200000b0   Data           8  heap_4.o(.data)
    .data                                    0x200000b8   Section       12  port.o(.data)
    ucMaxSysCallPriority                     0x200000b8   Data           1  port.o(.data)
    uxCriticalNesting                        0x200000bc   Data           4  port.o(.data)
    ulMaxPRIGROUPValue                       0x200000c0   Data           4  port.o(.data)
    .bss                                     0x200000c4   Section     1153  oled.o(.bss)
    buffer                                   0x200000c4   Data         129  oled.o(.bss)
    .bss                                     0x20000548   Section      120  guitar.o(.bss)
    keyDebounceTime                          0x20000548   Data          24  guitar.o(.bss)
    precomputedPeriods                       0x20000560   Data          96  guitar.o(.bss)
    .bss                                     0x200005c0   Section       84  i2c.o(.bss)
    .bss                                     0x20000614   Section       72  tim.o(.bss)
    .bss                                     0x2000065c   Section       72  stm32f1xx_hal_timebase_tim.o(.bss)
    .bss                                     0x200006a4   Section       64  queue.o(.bss)
    .bss                                     0x200006e4   Section     1220  tasks.o(.bss)
    pxReadyTasksLists                        0x200006e4   Data        1120  tasks.o(.bss)
    xDelayedTaskList1                        0x20000b44   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x20000b58   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x20000b6c   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x20000b80   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x20000b94   Data          20  tasks.o(.bss)
    .bss                                     0x20000ba8   Section      280  timers.o(.bss)
    xStaticTimerQueue                        0x20000ba8   Data          80  timers.o(.bss)
    ucStaticTimerQueueStorage                0x20000bf8   Data         160  timers.o(.bss)
    xActiveTimerList1                        0x20000c98   Data          20  timers.o(.bss)
    xActiveTimerList2                        0x20000cac   Data          20  timers.o(.bss)
    .bss                                     0x20000cc0   Section     1720  cmsis_os2.o(.bss)
    Idle_TCB                                 0x20000cc0   Data          92  cmsis_os2.o(.bss)
    Idle_Stack                               0x20000d1c   Data         512  cmsis_os2.o(.bss)
    Timer_TCB                                0x20000f1c   Data          92  cmsis_os2.o(.bss)
    Timer_Stack                              0x20000f78   Data        1024  cmsis_os2.o(.bss)
    .bss                                     0x20001378   Section     5000  heap_4.o(.bss)
    ucHeap                                   0x20001378   Data        5000  heap_4.o(.bss)
    .bss                                     0x20002700   Section       96  libspace.o(.bss)
    HEAP                                     0x20002760   Section      512  startup_stm32f103xb.o(HEAP)
    Heap_Mem                                 0x20002760   Data         512  startup_stm32f103xb.o(HEAP)
    STACK                                    0x20002960   Section     1024  startup_stm32f103xb.o(STACK)
    Stack_Mem                                0x20002960   Data        1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x20002d60   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_d                                0x08000161   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent                          0x08000161   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_percent_end                      0x08000167   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800016b   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800016d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x0800016f   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x08000171   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x08000171   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x08000171   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x08000171   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x08000171   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x08000171   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x08000173   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000173   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000179   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000179   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800017d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800017d   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000185   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000187   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000187   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800018b   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    SVC_Handler                              0x08000191   Thumb Code    32  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x080001b5   Thumb Code    28  port.o(.emb_text)
    PendSV_Handler                           0x080001d5   Thumb Code    72  port.o(.emb_text)
    vPortGetIPSR                             0x08000221   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x08000229   Thumb Code     8  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART1_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x08000243   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x08000245   Thumb Code     0  startup_stm32f103xb.o(.text)
    __2sprintf                               0x08000269   Thumb Code    38  __2sprintf.o(.text)
    _printf_int_dec                          0x08000295   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x0800030d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strcpy                                   0x08000495   Thumb Code    72  strcpy.o(.text)
    strlen                                   0x080004dd   Thumb Code    62  strlen.o(.text)
    strcat                                   0x0800051b   Thumb Code    24  strcat.o(.text)
    __aeabi_memcpy                           0x08000533   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000533   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000599   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x080005bd   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080005bd   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080005bd   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000605   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memset                           0x08000621   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr4                          0x08000631   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000631   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000631   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000635   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800067f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000681   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000683   Thumb Code     2  heapauxi.o(.text)
    _printf_int_common                       0x08000685   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_char_common                      0x08000743   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000769   Thumb Code    10  _sputc.o(.text)
    __aeabi_memclr                           0x08000773   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000773   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000777   Thumb Code     0  rt_memclr.o(.text)
    __user_libspace                          0x080007b9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080007b9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080007b9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080007c1   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x0800080b   Thumb Code    12  exit.o(.text)
    _sys_exit                                0x08000819   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x08000825   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000825   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x08000827   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x08000827   Thumb Code     0  indicate_semi.o(.text)
    DebugMon_Handler                         0x08000829   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x0800082b   Thumb Code     4  main.o(i.Error_Handler)
    Guitar_CheckKeyCombo                     0x08000831   Thumb Code   138  guitar.o(i.Guitar_CheckKeyCombo)
    Guitar_GetNoteFrequency                  0x080008c1   Thumb Code    22  guitar.o(i.Guitar_GetNoteFrequency)
    Guitar_GetPreciseNoteFrequency           0x080008d9   Thumb Code    66  guitar.o(i.Guitar_GetPreciseNoteFrequency)
    Guitar_Init                              0x08000925   Thumb Code    46  guitar.o(i.Guitar_Init)
    Guitar_IsKeyPressed                      0x0800095d   Thumb Code    56  guitar.o(i.Guitar_IsKeyPressed)
    Guitar_PlaySimpleChord                   0x08000999   Thumb Code   138  guitar.o(i.Guitar_PlaySimpleChord)
    Guitar_PrecomputePeriods                 0x08000a25   Thumb Code   180  guitar.o(i.Guitar_PrecomputePeriods)
    Guitar_ScanKeys                          0x08000ae9   Thumb Code   162  guitar.o(i.Guitar_ScanKeys)
    Guitar_SetFrequency                      0x08000b99   Thumb Code    58  guitar.o(i.Guitar_SetFrequency)
    Guitar_SetVolume                         0x08000be1   Thumb Code    36  guitar.o(i.Guitar_SetVolume)
    Guitar_SimpleMetronome                   0x08000c0d   Thumb Code   104  guitar.o(i.Guitar_SimpleMetronome)
    Guitar_SimpleVibrato                     0x08000c75   Thumb Code    66  guitar.o(i.Guitar_SimpleVibrato)
    Guitar_StopNote                          0x08000cb9   Thumb Code    16  guitar.o(i.Guitar_StopNote)
    Guitar_UltraFastSwitchNote               0x08000cd1   Thumb Code    80  guitar.o(i.Guitar_UltraFastSwitchNote)
    Guitar_UpdateDisplay                     0x08000d2d   Thumb Code   260  guitar.o(i.Guitar_UpdateDisplay)
    HAL_Delay                                0x08000edd   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08000f01   Thumb Code   446  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080010e1   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_TogglePin                       0x080010eb   Thumb Code    16  stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin)
    HAL_GPIO_WritePin                        0x080010fb   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001105   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08001111   Thumb Code   376  stm32f1xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_IsDeviceReady                    0x08001299   Thumb Code   354  stm32f1xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady)
    HAL_I2C_Master_Transmit                  0x08001405   Thumb Code   290  stm32f1xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MspInit                          0x08001531   Thumb Code    80  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x0800158d   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x0800159d   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080015c1   Thumb Code   132  stm32f1xx_hal_timebase_tim.o(i.HAL_InitTick)
    HAL_MspInit                              0x08001659   Thumb Code    62  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080016a1   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080016bd   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080016fd   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001721   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetClockConfig                   0x0800184d   Thumb Code    54  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x0800188d   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080018ad   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080018cd   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001919   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_TIMEx_BreakCallback                  0x08001c39   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x08001c3b   Thumb Code     2  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x08001c3d   Thumb Code    84  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08001c91   Thumb Code    92  stm32f1xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001cf9   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08001d55   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08001d7d   Thumb Code    76  stm32f1xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08001dd5   Thumb Code   220  stm32f1xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_IC_CaptureCallback               0x08001eb1   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08001eb3   Thumb Code   304  stm32f1xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08001fe5   Thumb Code    64  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08002031   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08002033   Thumb Code   204  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080020ff   Thumb Code    90  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08002159   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x0800215b   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x0800215d   Thumb Code   144  stm32f1xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x080021f9   Thumb Code    14  main.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x0800220d   Thumb Code     2  stm32f1xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HardFault_Handler                        0x0800220f   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    MX_FREERTOS_Init                         0x080024d9   Thumb Code    84  freertos.o(i.MX_FREERTOS_Init)
    MX_GPIO_Init                             0x08002541   Thumb Code   134  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x080025d5   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_TIM1_Init                             0x08002615   Thumb Code   196  tim.o(i.MX_TIM1_Init)
    MemManage_Handler                        0x080026e1   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080026e3   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    OLED_CheckBufferIntegrity                0x080026e5   Thumb Code    24  oled.o(i.OLED_CheckBufferIntegrity)
    OLED_Clear                               0x080026fd   Thumb Code    34  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x08002725   Thumb Code    86  oled.o(i.OLED_ClearArea)
    OLED_Init                                0x08002781   Thumb Code   182  oled.o(i.OLED_Init)
    OLED_PowerOnInit                         0x08002839   Thumb Code    72  oled.o(i.OLED_PowerOnInit)
    OLED_SetCursor                           0x08002885   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x080028a9   Thumb Code    52  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x080028e5   Thumb Code   182  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x080029a1   Thumb Code    44  oled.o(i.OLED_ShowString)
    OLED_Update                              0x080029cd   Thumb Code    68  oled.o(i.OLED_Update)
    OLED_WriteCommand                        0x08002a35   Thumb Code    40  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x08002a61   Thumb Code    56  oled.o(i.OLED_WriteData)
    StartAudioTask                           0x08002aa1   Thumb Code    70  freertos.o(i.StartAudioTask)
    StartDisplayTask                         0x08002af1   Thumb Code   172  freertos.o(i.StartDisplayTask)
    StartKeyScanTask                         0x08002bc5   Thumb Code    12  freertos.o(i.StartKeyScanTask)
    SysTick_Handler                          0x08002bd1   Thumb Code    26  cmsis_os2.o(i.SysTick_Handler)
    SystemClock_Config                       0x08002beb   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x08002c49   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    TIM4_IRQHandler                          0x08002c4d   Thumb Code     6  stm32f1xx_it.o(i.TIM4_IRQHandler)
    TIM_Base_SetConfig                       0x08002c59   Thumb Code   108  stm32f1xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08002cd1   Thumb Code    26  stm32f1xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08002ceb   Thumb Code    20  stm32f1xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08002d61   Thumb Code    84  stm32f1xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UsageFault_Handler                       0x08002e9b   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    _is_digit                                0x08002ebd   Thumb Code    14  __printf_wp.o(i._is_digit)
    main                                     0x08002ecb   Thumb Code    38  main.o(i.main)
    osDelay                                  0x08002ef1   Thumb Code    26  cmsis_os2.o(i.osDelay)
    osKernelInitialize                       0x08002f0d   Thumb Code    32  cmsis_os2.o(i.osKernelInitialize)
    osKernelStart                            0x08002f31   Thumb Code    48  cmsis_os2.o(i.osKernelStart)
    osMessageQueueGet                        0x08002f69   Thumb Code    96  cmsis_os2.o(i.osMessageQueueGet)
    osMessageQueueNew                        0x08002fcd   Thumb Code    88  cmsis_os2.o(i.osMessageQueueNew)
    osMessageQueuePut                        0x08003025   Thumb Code   100  cmsis_os2.o(i.osMessageQueuePut)
    osMutexAcquire                           0x0800308d   Thumb Code    82  cmsis_os2.o(i.osMutexAcquire)
    osMutexNew                               0x080030df   Thumb Code   104  cmsis_os2.o(i.osMutexNew)
    osMutexRelease                           0x08003147   Thumb Code    66  cmsis_os2.o(i.osMutexRelease)
    osThreadNew                              0x08003189   Thumb Code   136  cmsis_os2.o(i.osThreadNew)
    pvPortMalloc                             0x080039b9   Thumb Code   216  heap_4.o(i.pvPortMalloc)
    pvTaskIncrementMutexHeldCount            0x08003a95   Thumb Code    18  tasks.o(i.pvTaskIncrementMutexHeldCount)
    pxPortInitialiseStack                    0x08003aad   Thumb Code    32  port.o(i.pxPortInitialiseStack)
    uxListRemove                             0x08003ad1   Thumb Code    38  list.o(i.uxListRemove)
    vApplicationGetIdleTaskMemory            0x08003af9   Thumb Code    16  cmsis_os2.o(i.vApplicationGetIdleTaskMemory)
    vApplicationGetTimerTaskMemory           0x08003b0d   Thumb Code    18  cmsis_os2.o(i.vApplicationGetTimerTaskMemory)
    vListInitialise                          0x08003b25   Thumb Code    22  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08003b3b   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08003b41   Thumb Code    48  list.o(i.vListInsert)
    vListInsertEnd                           0x08003b71   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08003b89   Thumb Code    54  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08003bc9   Thumb Code    34  port.o(i.vPortExitCritical)
    vPortFree                                0x08003bf1   Thumb Code    94  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x08003c55   Thumb Code    32  port.o(i.vPortSetupTimerInterrupt)
    vPortValidateInterruptPriority           0x08003c79   Thumb Code    74  port.o(i.vPortValidateInterruptPriority)
    vQueueAddToRegistry                      0x08003ccd   Thumb Code    34  queue.o(i.vQueueAddToRegistry)
    vQueueWaitForMessageRestricted           0x08003cf5   Thumb Code    68  queue.o(i.vQueueWaitForMessageRestricted)
    vTaskDelay                               0x08003d39   Thumb Code    66  tasks.o(i.vTaskDelay)
    vTaskInternalSetTimeOutState             0x08003d85   Thumb Code    12  tasks.o(i.vTaskInternalSetTimeOutState)
    vTaskMissedYield                         0x08003d95   Thumb Code     8  tasks.o(i.vTaskMissedYield)
    vTaskPlaceOnEventList                    0x08003da1   Thumb Code    44  tasks.o(i.vTaskPlaceOnEventList)
    vTaskPlaceOnEventListRestricted          0x08003dd1   Thumb Code    52  tasks.o(i.vTaskPlaceOnEventListRestricted)
    vTaskPriorityDisinheritAfterTimeout      0x08003e09   Thumb Code   134  tasks.o(i.vTaskPriorityDisinheritAfterTimeout)
    vTaskStartScheduler                      0x08003e99   Thumb Code   118  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x08003f21   Thumb Code    10  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08003f31   Thumb Code    90  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x08003f95   Thumb Code   154  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x0800403d   Thumb Code    38  port.o(i.xPortSysTickHandler)
    xQueueCreateMutex                        0x08004069   Thumb Code    22  queue.o(i.xQueueCreateMutex)
    xQueueCreateMutexStatic                  0x0800407f   Thumb Code    26  queue.o(i.xQueueCreateMutexStatic)
    xQueueGenericCreate                      0x08004099   Thumb Code    66  queue.o(i.xQueueGenericCreate)
    xQueueGenericCreateStatic                0x080040db   Thumb Code   102  queue.o(i.xQueueGenericCreateStatic)
    xQueueGenericReset                       0x08004141   Thumb Code   132  queue.o(i.xQueueGenericReset)
    xQueueGenericSend                        0x080041c9   Thumb Code   346  queue.o(i.xQueueGenericSend)
    xQueueGenericSendFromISR                 0x08004329   Thumb Code   190  queue.o(i.xQueueGenericSendFromISR)
    xQueueGiveMutexRecursive                 0x080043e7   Thumb Code    62  queue.o(i.xQueueGiveMutexRecursive)
    xQueueReceive                            0x08004425   Thumb Code   308  queue.o(i.xQueueReceive)
    xQueueReceiveFromISR                     0x0800455d   Thumb Code   154  queue.o(i.xQueueReceiveFromISR)
    xQueueSemaphoreTake                      0x080045f9   Thumb Code   372  queue.o(i.xQueueSemaphoreTake)
    xQueueTakeMutexRecursive                 0x08004771   Thumb Code    64  queue.o(i.xQueueTakeMutexRecursive)
    xTaskCheckForTimeOut                     0x080047b1   Thumb Code   112  tasks.o(i.xTaskCheckForTimeOut)
    xTaskCreate                              0x08004825   Thumb Code    90  tasks.o(i.xTaskCreate)
    xTaskCreateStatic                        0x0800487f   Thumb Code    86  tasks.o(i.xTaskCreateStatic)
    xTaskGetCurrentTaskHandle                0x080048d5   Thumb Code     6  tasks.o(i.xTaskGetCurrentTaskHandle)
    xTaskGetSchedulerState                   0x080048e1   Thumb Code    22  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x080048fd   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x08004909   Thumb Code   192  tasks.o(i.xTaskIncrementTick)
    xTaskPriorityDisinherit                  0x080049d1   Thumb Code   118  tasks.o(i.xTaskPriorityDisinherit)
    xTaskPriorityInherit                     0x08004a51   Thumb Code   112  tasks.o(i.xTaskPriorityInherit)
    xTaskRemoveFromEventList                 0x08004ac9   Thumb Code    98  tasks.o(i.xTaskRemoveFromEventList)
    xTaskResumeAll                           0x08004b39   Thumb Code   182  tasks.o(i.xTaskResumeAll)
    xTimerCreateTimerTask                    0x08004bfd   Thumb Code    78  timers.o(i.xTimerCreateTimerTask)
    xTimerGenericCommand                     0x08004c5d   Thumb Code    98  timers.o(i.xTimerGenericCommand)
    __aeabi_d2f                              0x08004cc5   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08004cc5   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08004d29   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08004d29   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08004e79   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_ddiv                             0x08004e89   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08004e89   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2uiz                            0x08005139   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08005139   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x08005193   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08005193   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __fpl_dnaninf                            0x080051b9   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08005255   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_dsub                             0x08005261   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08005261   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_fadd                             0x08005435   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08005435   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_f2uiz                            0x080054f9   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x080054f9   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __fpl_fnaninf                            0x08005537   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x080055c3   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x080055cd   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x080055cd   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __fpl_return_NaN                         0x080056b7   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800571b   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x08005777   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    OLED_F8x16                               0x080057a6   Data        1520  oled_data.o(.constdata)
    __I$use$fp                               0x080057a6   Number         0  usenofp.o(x$fpl$usenofp)
    OLED_F6x8                                0x08005d96   Data         570  oled_data.o(.constdata)
    noteFrequencies                          0x08005fe0   Data          12  guitar.o(.constdata)
    defaultTask_attributes                   0x08006028   Data          36  freertos.o(.constdata)
    AudioTask_attributes                     0x0800604c   Data          36  freertos.o(.constdata)
    DisplayTask_attributes                   0x08006070   Data          36  freertos.o(.constdata)
    KeyEventQueue_attributes                 0x08006094   Data          24  freertos.o(.constdata)
    AudioCommandQueue_attributes             0x080060ac   Data          24  freertos.o(.constdata)
    I2C_Mutex_attributes                     0x080060c4   Data          16  freertos.o(.constdata)
    AHBPrescTable                            0x080060e6   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x080060f6   Data           8  system_stm32f1xx.o(.constdata)
    Region$$Table$$Base                      0x0800619c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080061bc   Number         0  anon$$obj.o(Region$$Table)
    guitarStatus                             0x2000000c   Data          16  guitar.o(.data)
    defaultTaskHandle                        0x2000001c   Data           4  freertos.o(.data)
    AudioTaskHandle                          0x20000020   Data           4  freertos.o(.data)
    DisplayTaskHandle                        0x20000024   Data           4  freertos.o(.data)
    KeyEventQueueHandle                      0x20000028   Data           4  freertos.o(.data)
    AudioCommandQueueHandle                  0x2000002c   Data           4  freertos.o(.data)
    I2C_MutexHandle                          0x20000030   Data           4  freertos.o(.data)
    uwTickFreq                               0x20000034   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000038   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x2000003c   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x20000040   Data           4  system_stm32f1xx.o(.data)
    pxCurrentTCB                             0x20000044   Data           4  tasks.o(.data)
    OLED_DisplayBuf                          0x20000145   Data        1024  oled.o(.bss)
    hi2c1                                    0x200005c0   Data          84  i2c.o(.bss)
    htim1                                    0x20000614   Data          72  tim.o(.bss)
    htim4                                    0x2000065c   Data          72  stm32f1xx_hal_timebase_tim.o(.bss)
    xQueueRegistry                           0x200006a4   Data          64  queue.o(.bss)
    __libspace_start                         0x20002700   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20002760   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006280, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x000061bc, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x00000008   Code   RO         5247  * !!!main             c_w.l(__main.o)
    0x080000f4   0x00000034   Code   RO         5657    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x0000001a   Code   RO         5659    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x00000002   PAD
    0x08000144   0x0000001c   Code   RO         5661    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x00000000   Code   RO         5228    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x08000160   0x00000006   Code   RO         5227    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000166   0x00000004   Code   RO         5372    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800016a   0x00000002   Code   RO         5522    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800016c   0x00000000   Code   RO         5535    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5537    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5540    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5542    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5544    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5547    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5549    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5551    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5553    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5555    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5557    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5559    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5561    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5563    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5565    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5567    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5571    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5573    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5575    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800016c   0x00000000   Code   RO         5577    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800016c   0x00000002   Code   RO         5578    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x0800016e   0x00000002   Code   RO         5598    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000170   0x00000000   Code   RO         5612    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x08000170   0x00000000   Code   RO         5615    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000170   0x00000000   Code   RO         5618    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000170   0x00000000   Code   RO         5620    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x08000170   0x00000000   Code   RO         5623    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000170   0x00000002   Code   RO         5624    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000172   0x00000000   Code   RO         5311    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000172   0x00000000   Code   RO         5433    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000172   0x00000006   Code   RO         5445    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000178   0x00000000   Code   RO         5435    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000178   0x00000004   Code   RO         5436    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800017c   0x00000000   Code   RO         5438    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800017c   0x00000008   Code   RO         5439    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000184   0x00000002   Code   RO         5527    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000186   0x00000000   Code   RO         5580    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000186   0x00000004   Code   RO         5581    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800018a   0x00000006   Code   RO         5582    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000190   0x00000096   Code   RO         5116    .emb_text           port.o
    0x08000226   0x00000002   PAD
    0x08000228   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x08000268   0x0000002c   Code   RO         5199    .text               c_w.l(__2sprintf.o)
    0x08000294   0x00000078   Code   RO         5205    .text               c_w.l(_printf_dec.o)
    0x0800030c   0x00000188   Code   RO         5224    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000494   0x00000048   Code   RO         5229    .text               c_w.l(strcpy.o)
    0x080004dc   0x0000003e   Code   RO         5231    .text               c_w.l(strlen.o)
    0x0800051a   0x00000018   Code   RO         5233    .text               c_w.l(strcat.o)
    0x08000532   0x0000008a   Code   RO         5235    .text               c_w.l(rt_memcpy_v6.o)
    0x080005bc   0x00000064   Code   RO         5237    .text               c_w.l(rt_memcpy_w.o)
    0x08000620   0x00000010   Code   RO         5241    .text               c_w.l(aeabi_memset.o)
    0x08000630   0x0000004e   Code   RO         5243    .text               c_w.l(rt_memclr_w.o)
    0x0800067e   0x00000006   Code   RO         5245    .text               c_w.l(heapauxi.o)
    0x08000684   0x000000b2   Code   RO         5320    .text               c_w.l(_printf_intcommon.o)
    0x08000736   0x00000002   PAD
    0x08000738   0x00000030   Code   RO         5324    .text               c_w.l(_printf_char_common.o)
    0x08000768   0x0000000a   Code   RO         5326    .text               c_w.l(_sputc.o)
    0x08000772   0x00000044   Code   RO         5382    .text               c_w.l(rt_memclr.o)
    0x080007b6   0x00000002   PAD
    0x080007b8   0x00000008   Code   RO         5473    .text               c_w.l(libspace.o)
    0x080007c0   0x0000004a   Code   RO         5476    .text               c_w.l(sys_stackheap_outer.o)
    0x0800080a   0x0000000c   Code   RO         5515    .text               c_w.l(exit.o)
    0x08000816   0x00000002   PAD
    0x08000818   0x0000000c   Code   RO         5590    .text               c_w.l(sys_exit.o)
    0x08000824   0x00000002   Code   RO         5601    .text               c_w.l(use_no_semi.o)
    0x08000826   0x00000000   Code   RO         5603    .text               c_w.l(indicate_semi.o)
    0x08000826   0x00000002   Code   RO          850    i.BusFault_Handler  stm32f1xx_it.o
    0x08000828   0x00000002   Code   RO          851    i.DebugMon_Handler  stm32f1xx_it.o
    0x0800082a   0x00000004   Code   RO          631    i.Error_Handler     main.o
    0x0800082e   0x00000002   PAD
    0x08000830   0x00000090   Code   RO          389    i.Guitar_CheckKeyCombo  guitar.o
    0x080008c0   0x00000016   Code   RO          392    i.Guitar_GetNoteFrequency  guitar.o
    0x080008d6   0x00000002   PAD
    0x080008d8   0x0000004c   Code   RO          393    i.Guitar_GetPreciseNoteFrequency  guitar.o
    0x08000924   0x00000038   Code   RO          394    i.Guitar_Init       guitar.o
    0x0800095c   0x0000003c   Code   RO          395    i.Guitar_IsKeyPressed  guitar.o
    0x08000998   0x0000008a   Code   RO          398    i.Guitar_PlaySimpleChord  guitar.o
    0x08000a22   0x00000002   PAD
    0x08000a24   0x000000c4   Code   RO          400    i.Guitar_PrecomputePeriods  guitar.o
    0x08000ae8   0x000000b0   Code   RO          403    i.Guitar_ScanKeys   guitar.o
    0x08000b98   0x00000048   Code   RO          404    i.Guitar_SetFrequency  guitar.o
    0x08000be0   0x0000002c   Code   RO          406    i.Guitar_SetVolume  guitar.o
    0x08000c0c   0x00000068   Code   RO          407    i.Guitar_SimpleMetronome  guitar.o
    0x08000c74   0x00000042   Code   RO          409    i.Guitar_SimpleVibrato  guitar.o
    0x08000cb6   0x00000002   PAD
    0x08000cb8   0x00000018   Code   RO          410    i.Guitar_StopNote   guitar.o
    0x08000cd0   0x0000005c   Code   RO          414    i.Guitar_UltraFastSwitchNote  guitar.o
    0x08000d2c   0x000001b0   Code   RO          415    i.Guitar_UpdateDisplay  guitar.o
    0x08000edc   0x00000024   Code   RO         1993    i.HAL_Delay         stm32f1xx_hal.o
    0x08000f00   0x000001e0   Code   RO         2303    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x080010e0   0x0000000a   Code   RO         2305    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x080010ea   0x00000010   Code   RO         2306    i.HAL_GPIO_TogglePin  stm32f1xx_hal_gpio.o
    0x080010fa   0x0000000a   Code   RO         2307    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08001104   0x0000000c   Code   RO         1997    i.HAL_GetTick       stm32f1xx_hal.o
    0x08001110   0x00000188   Code   RO         2974    i.HAL_I2C_Init      stm32f1xx_hal_i2c.o
    0x08001298   0x0000016c   Code   RO         2975    i.HAL_I2C_IsDeviceReady  stm32f1xx_hal_i2c.o
    0x08001404   0x0000012c   Code   RO         2987    i.HAL_I2C_Master_Transmit  stm32f1xx_hal_i2c.o
    0x08001530   0x0000005c   Code   RO          761    i.HAL_I2C_MspInit   i2c.o
    0x0800158c   0x00000010   Code   RO         2003    i.HAL_IncTick       stm32f1xx_hal.o
    0x0800159c   0x00000024   Code   RO         2004    i.HAL_Init          stm32f1xx_hal.o
    0x080015c0   0x00000098   Code   RO          938    i.HAL_InitTick      stm32f1xx_hal_timebase_tim.o
    0x08001658   0x00000048   Code   RO          914    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x080016a0   0x0000001a   Code   RO         2463    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x080016ba   0x00000002   PAD
    0x080016bc   0x00000040   Code   RO         2469    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080016fc   0x00000024   Code   RO         2470    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08001720   0x0000012c   Code   RO         2161    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x0800184c   0x00000040   Code   RO         2165    i.HAL_RCC_GetClockConfig  stm32f1xx_hal_rcc.o
    0x0800188c   0x00000020   Code   RO         2168    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x080018ac   0x00000020   Code   RO         2169    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x080018cc   0x0000004c   Code   RO         2170    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08001918   0x00000320   Code   RO         2173    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08001c38   0x00000002   Code   RO         1719    i.HAL_TIMEx_BreakCallback  stm32f1xx_hal_tim_ex.o
    0x08001c3a   0x00000002   Code   RO         1720    i.HAL_TIMEx_CommutCallback  stm32f1xx_hal_tim_ex.o
    0x08001c3c   0x00000054   Code   RO         1722    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f1xx_hal_tim_ex.o
    0x08001c90   0x00000068   Code   RO         1738    i.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x08001cf8   0x0000005a   Code   RO         1015    i.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x08001d52   0x00000002   PAD
    0x08001d54   0x00000028   Code   RO          803    i.HAL_TIM_Base_MspInit  tim.o
    0x08001d7c   0x00000058   Code   RO         1020    i.HAL_TIM_Base_Start_IT  stm32f1xx_hal_tim.o
    0x08001dd4   0x000000dc   Code   RO         1024    i.HAL_TIM_ConfigClockSource  stm32f1xx_hal_tim.o
    0x08001eb0   0x00000002   Code   RO         1049    i.HAL_TIM_IC_CaptureCallback  stm32f1xx_hal_tim.o
    0x08001eb2   0x00000130   Code   RO         1063    i.HAL_TIM_IRQHandler  stm32f1xx_hal_tim.o
    0x08001fe2   0x00000002   PAD
    0x08001fe4   0x0000004c   Code   RO          804    i.HAL_TIM_MspPostInit  tim.o
    0x08002030   0x00000002   Code   RO         1066    i.HAL_TIM_OC_DelayElapsedCallback  stm32f1xx_hal_tim.o
    0x08002032   0x000000cc   Code   RO         1087    i.HAL_TIM_PWM_ConfigChannel  stm32f1xx_hal_tim.o
    0x080020fe   0x0000005a   Code   RO         1090    i.HAL_TIM_PWM_Init  stm32f1xx_hal_tim.o
    0x08002158   0x00000002   Code   RO         1092    i.HAL_TIM_PWM_MspInit  stm32f1xx_hal_tim.o
    0x0800215a   0x00000002   Code   RO         1093    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f1xx_hal_tim.o
    0x0800215c   0x0000009c   Code   RO         1095    i.HAL_TIM_PWM_Start  stm32f1xx_hal_tim.o
    0x080021f8   0x00000014   Code   RO          632    i.HAL_TIM_PeriodElapsedCallback  main.o
    0x0800220c   0x00000002   Code   RO         1106    i.HAL_TIM_TriggerCallback  stm32f1xx_hal_tim.o
    0x0800220e   0x00000002   Code   RO          852    i.HardFault_Handler  stm32f1xx_it.o
    0x08002210   0x0000002e   Code   RO         3017    i.I2C_IsAcknowledgeFailed  stm32f1xx_hal_i2c.o
    0x0800223e   0x00000002   PAD
    0x08002240   0x0000009c   Code   RO         3021    i.I2C_MasterRequestWrite  stm32f1xx_hal_i2c.o
    0x080022dc   0x00000056   Code   RO         3032    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08002332   0x00000002   PAD
    0x08002334   0x00000090   Code   RO         3033    i.I2C_WaitOnFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x080023c4   0x000000bc   Code   RO         3034    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x08002480   0x00000056   Code   RO         3036    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f1xx_hal_i2c.o
    0x080024d6   0x00000002   PAD
    0x080024d8   0x00000068   Code   RO          701    i.MX_FREERTOS_Init  freertos.o
    0x08002540   0x00000094   Code   RO          677    i.MX_GPIO_Init      gpio.o
    0x080025d4   0x00000040   Code   RO          762    i.MX_I2C1_Init      i2c.o
    0x08002614   0x000000cc   Code   RO          805    i.MX_TIM1_Init      tim.o
    0x080026e0   0x00000002   Code   RO          853    i.MemManage_Handler  stm32f1xx_it.o
    0x080026e2   0x00000002   Code   RO          854    i.NMI_Handler       stm32f1xx_it.o
    0x080026e4   0x00000018   Code   RO           13    i.OLED_CheckBufferIntegrity  oled.o
    0x080026fc   0x00000028   Code   RO           14    i.OLED_Clear        oled.o
    0x08002724   0x0000005c   Code   RO           15    i.OLED_ClearArea    oled.o
    0x08002780   0x000000b6   Code   RO           27    i.OLED_Init         oled.o
    0x08002836   0x00000002   PAD
    0x08002838   0x0000004c   Code   RO           30    i.OLED_PowerOnInit  oled.o
    0x08002884   0x00000022   Code   RO           34    i.OLED_SetCursor    oled.o
    0x080028a6   0x00000002   PAD
    0x080028a8   0x0000003c   Code   RO           36    i.OLED_ShowChar     oled.o
    0x080028e4   0x000000bc   Code   RO           39    i.OLED_ShowImage    oled.o
    0x080029a0   0x0000002c   Code   RO           42    i.OLED_ShowString   oled.o
    0x080029cc   0x00000068   Code   RO           43    i.OLED_Update       oled.o
    0x08002a34   0x0000002c   Code   RO           47    i.OLED_WriteCommand  oled.o
    0x08002a60   0x00000040   Code   RO           48    i.OLED_WriteData    oled.o
    0x08002aa0   0x00000050   Code   RO          702    i.StartAudioTask    freertos.o
    0x08002af0   0x000000d4   Code   RO          703    i.StartDisplayTask  freertos.o
    0x08002bc4   0x0000000c   Code   RO          704    i.StartKeyScanTask  freertos.o
    0x08002bd0   0x0000001a   Code   RO         4548    i.SysTick_Handler   cmsis_os2.o
    0x08002bea   0x0000005e   Code   RO          633    i.SystemClock_Config  main.o
    0x08002c48   0x00000002   Code   RO         3401    i.SystemInit        system_stm32f1xx.o
    0x08002c4a   0x00000002   PAD
    0x08002c4c   0x0000000c   Code   RO          855    i.TIM4_IRQHandler   stm32f1xx_it.o
    0x08002c58   0x00000078   Code   RO         1108    i.TIM_Base_SetConfig  stm32f1xx_hal_tim.o
    0x08002cd0   0x0000001a   Code   RO         1109    i.TIM_CCxChannelCmd  stm32f1xx_hal_tim.o
    0x08002cea   0x00000014   Code   RO         1119    i.TIM_ETR_SetConfig  stm32f1xx_hal_tim.o
    0x08002cfe   0x00000010   Code   RO         1120    i.TIM_ITRx_SetConfig  stm32f1xx_hal_tim.o
    0x08002d0e   0x00000002   PAD
    0x08002d10   0x00000050   Code   RO         1121    i.TIM_OC1_SetConfig  stm32f1xx_hal_tim.o
    0x08002d60   0x00000058   Code   RO         1122    i.TIM_OC2_SetConfig  stm32f1xx_hal_tim.o
    0x08002db8   0x00000058   Code   RO         1123    i.TIM_OC3_SetConfig  stm32f1xx_hal_tim.o
    0x08002e10   0x00000044   Code   RO         1124    i.TIM_OC4_SetConfig  stm32f1xx_hal_tim.o
    0x08002e54   0x00000022   Code   RO         1126    i.TIM_TI1_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08002e76   0x00000024   Code   RO         1128    i.TIM_TI2_ConfigInputStage  stm32f1xx_hal_tim.o
    0x08002e9a   0x00000002   Code   RO          856    i.UsageFault_Handler  stm32f1xx_it.o
    0x08002e9c   0x00000020   Code   RO         2476    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002ebc   0x0000000e   Code   RO         5217    i._is_digit         c_w.l(__printf_wp.o)
    0x08002eca   0x00000026   Code   RO          634    i.main              main.o
    0x08002ef0   0x0000001a   Code   RO         4550    i.osDelay           cmsis_os2.o
    0x08002f0a   0x00000002   PAD
    0x08002f0c   0x00000024   Code   RO         4564    i.osKernelInitialize  cmsis_os2.o
    0x08002f30   0x00000038   Code   RO         4567    i.osKernelStart     cmsis_os2.o
    0x08002f68   0x00000064   Code   RO         4579    i.osMessageQueueGet  cmsis_os2.o
    0x08002fcc   0x00000058   Code   RO         4584    i.osMessageQueueNew  cmsis_os2.o
    0x08003024   0x00000068   Code   RO         4585    i.osMessageQueuePut  cmsis_os2.o
    0x0800308c   0x00000052   Code   RO         4587    i.osMutexAcquire    cmsis_os2.o
    0x080030de   0x00000068   Code   RO         4590    i.osMutexNew        cmsis_os2.o
    0x08003146   0x00000042   Code   RO         4591    i.osMutexRelease    cmsis_os2.o
    0x08003188   0x00000088   Code   RO         4609    i.osThreadNew       cmsis_os2.o
    0x08003210   0x0000005c   Code   RO         4009    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x0800326c   0x000000d0   Code   RO         4010    i.prvAddNewTaskToReadyList  tasks.o
    0x0800333c   0x00000058   Code   RO         4374    i.prvCheckForValidListAndQueue  timers.o
    0x08003394   0x00000026   Code   RO         3606    i.prvCopyDataFromQueue  queue.o
    0x080033ba   0x0000006c   Code   RO         3607    i.prvCopyDataToQueue  queue.o
    0x08003426   0x00000034   Code   RO         4011    i.prvDeleteTCB      tasks.o
    0x0800345a   0x00000002   PAD
    0x0800345c   0x00000048   Code   RO         5061    i.prvHeapInit       heap_4.o
    0x080034a4   0x00000060   Code   RO         4012    i.prvIdleTask       tasks.o
    0x08003504   0x00000016   Code   RO         3608    i.prvInitialiseMutex  queue.o
    0x0800351a   0x00000002   PAD
    0x0800351c   0x00000022   Code   RO         3609    i.prvInitialiseNewQueue  queue.o
    0x0800353e   0x00000002   PAD
    0x08003540   0x000000b0   Code   RO         4013    i.prvInitialiseNewTask  tasks.o
    0x080035f0   0x0000004c   Code   RO         5062    i.prvInsertBlockIntoFreeList  heap_4.o
    0x0800363c   0x00000038   Code   RO         4376    i.prvInsertTimerInActiveList  timers.o
    0x08003674   0x0000001c   Code   RO         3610    i.prvIsQueueEmpty   queue.o
    0x08003690   0x000000fc   Code   RO         4377    i.prvProcessReceivedCommands  timers.o
    0x0800378c   0x000000c0   Code   RO         4378    i.prvProcessTimerOrBlockTask  timers.o
    0x0800384c   0x00000020   Code   RO         4015    i.prvResetNextTaskUnblockTime  tasks.o
    0x0800386c   0x00000028   Code   RO         4379    i.prvSampleTimeNow  timers.o
    0x08003894   0x0000006c   Code   RO         4380    i.prvSwitchTimerLists  timers.o
    0x08003900   0x00000028   Code   RO         5117    i.prvTaskExitError  port.o
    0x08003928   0x00000024   Code   RO         4381    i.prvTimerTask      timers.o
    0x0800394c   0x0000006a   Code   RO         3611    i.prvUnlockQueue    queue.o
    0x080039b6   0x00000002   PAD
    0x080039b8   0x000000dc   Code   RO         5063    i.pvPortMalloc      heap_4.o
    0x08003a94   0x00000018   Code   RO         4018    i.pvTaskIncrementMutexHeldCount  tasks.o
    0x08003aac   0x00000024   Code   RO         5118    i.pxPortInitialiseStack  port.o
    0x08003ad0   0x00000026   Code   RO         3566    i.uxListRemove      list.o
    0x08003af6   0x00000002   PAD
    0x08003af8   0x00000014   Code   RO         4621    i.vApplicationGetIdleTaskMemory  cmsis_os2.o
    0x08003b0c   0x00000018   Code   RO         4622    i.vApplicationGetTimerTaskMemory  cmsis_os2.o
    0x08003b24   0x00000016   Code   RO         3567    i.vListInitialise   list.o
    0x08003b3a   0x00000006   Code   RO         3568    i.vListInitialiseItem  list.o
    0x08003b40   0x00000030   Code   RO         3569    i.vListInsert       list.o
    0x08003b70   0x00000018   Code   RO         3570    i.vListInsertEnd    list.o
    0x08003b88   0x00000040   Code   RO         5120    i.vPortEnterCritical  port.o
    0x08003bc8   0x00000028   Code   RO         5121    i.vPortExitCritical  port.o
    0x08003bf0   0x00000064   Code   RO         5064    i.vPortFree         heap_4.o
    0x08003c54   0x00000024   Code   RO         5122    i.vPortSetupTimerInterrupt  port.o
    0x08003c78   0x00000054   Code   RO         5123    i.vPortValidateInterruptPriority  port.o
    0x08003ccc   0x00000028   Code   RO         3617    i.vQueueAddToRegistry  queue.o
    0x08003cf4   0x00000044   Code   RO         3621    i.vQueueWaitForMessageRestricted  queue.o
    0x08003d38   0x0000004c   Code   RO         4028    i.vTaskDelay        tasks.o
    0x08003d84   0x00000010   Code   RO         4033    i.vTaskInternalSetTimeOutState  tasks.o
    0x08003d94   0x0000000c   Code   RO         4034    i.vTaskMissedYield  tasks.o
    0x08003da0   0x00000030   Code   RO         4036    i.vTaskPlaceOnEventList  tasks.o
    0x08003dd0   0x00000038   Code   RO         4037    i.vTaskPlaceOnEventListRestricted  tasks.o
    0x08003e08   0x00000090   Code   RO         4039    i.vTaskPriorityDisinheritAfterTimeout  tasks.o
    0x08003e98   0x00000088   Code   RO         4045    i.vTaskStartScheduler  tasks.o
    0x08003f20   0x00000010   Code   RO         4047    i.vTaskSuspendAll   tasks.o
    0x08003f30   0x00000064   Code   RO         4048    i.vTaskSwitchContext  tasks.o
    0x08003f94   0x000000a8   Code   RO         5124    i.xPortStartScheduler  port.o
    0x0800403c   0x0000002c   Code   RO         5125    i.xPortSysTickHandler  port.o
    0x08004068   0x00000016   Code   RO         3624    i.xQueueCreateMutex  queue.o
    0x0800407e   0x0000001a   Code   RO         3625    i.xQueueCreateMutexStatic  queue.o
    0x08004098   0x00000042   Code   RO         3626    i.xQueueGenericCreate  queue.o
    0x080040da   0x00000066   Code   RO         3627    i.xQueueGenericCreateStatic  queue.o
    0x08004140   0x00000088   Code   RO         3628    i.xQueueGenericReset  queue.o
    0x080041c8   0x00000160   Code   RO         3629    i.xQueueGenericSend  queue.o
    0x08004328   0x000000be   Code   RO         3630    i.xQueueGenericSendFromISR  queue.o
    0x080043e6   0x0000003e   Code   RO         3634    i.xQueueGiveMutexRecursive  queue.o
    0x08004424   0x00000138   Code   RO         3639    i.xQueueReceive     queue.o
    0x0800455c   0x0000009a   Code   RO         3640    i.xQueueReceiveFromISR  queue.o
    0x080045f6   0x00000002   PAD
    0x080045f8   0x00000178   Code   RO         3641    i.xQueueSemaphoreTake  queue.o
    0x08004770   0x00000040   Code   RO         3642    i.xQueueTakeMutexRecursive  queue.o
    0x080047b0   0x00000074   Code   RO         4050    i.xTaskCheckForTimeOut  tasks.o
    0x08004824   0x0000005a   Code   RO         4051    i.xTaskCreate       tasks.o
    0x0800487e   0x00000056   Code   RO         4052    i.xTaskCreateStatic  tasks.o
    0x080048d4   0x0000000c   Code   RO         4055    i.xTaskGetCurrentTaskHandle  tasks.o
    0x080048e0   0x0000001c   Code   RO         4056    i.xTaskGetSchedulerState  tasks.o
    0x080048fc   0x0000000c   Code   RO         4057    i.xTaskGetTickCount  tasks.o
    0x08004908   0x000000c8   Code   RO         4059    i.xTaskIncrementTick  tasks.o
    0x080049d0   0x00000080   Code   RO         4062    i.xTaskPriorityDisinherit  tasks.o
    0x08004a50   0x00000078   Code   RO         4063    i.xTaskPriorityInherit  tasks.o
    0x08004ac8   0x00000070   Code   RO         4064    i.xTaskRemoveFromEventList  tasks.o
    0x08004b38   0x000000c4   Code   RO         4065    i.xTaskResumeAll    tasks.o
    0x08004bfc   0x00000060   Code   RO         4390    i.xTimerCreateTimerTask  timers.o
    0x08004c5c   0x00000068   Code   RO         4391    i.xTimerGenericCommand  timers.o
    0x08004cc4   0x00000062   Code   RO         5249    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08004d26   0x00000002   PAD
    0x08004d28   0x00000150   Code   RO         5251    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08004e78   0x00000010   Code   RO         5384    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08004e88   0x000002b0   Code   RO         5258    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08005138   0x0000005a   Code   RO         5265    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x08005192   0x00000026   Code   RO         5269    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x080051b8   0x0000009c   Code   RO         5388    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x08005254   0x0000000c   Code   RO         5390    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08005260   0x000001d4   Code   RO         5253    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08005434   0x000000c4   Code   RO         5283    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080054f8   0x0000003e   Code   RO         5289    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08005536   0x0000008c   Code   RO         5394    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x080055c2   0x0000000a   Code   RO         5396    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x080055cc   0x000000ea   Code   RO         5285    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x080056b6   0x00000064   Code   RO         5467    x$fpl$retnan        fz_ws.l(retnan.o)
    0x0800571a   0x0000005c   Code   RO         5299    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x08005776   0x00000030   Code   RO         5525    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x080057a6   0x00000000   Code   RO         5406    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x080057a6   0x000005f0   Data   RO          365    .constdata          oled_data.o
    0x08005d96   0x0000023a   Data   RO          366    .constdata          oled_data.o
    0x08005fd0   0x00000058   Data   RO          418    .constdata          guitar.o
    0x08006028   0x000000ac   Data   RO          705    .constdata          freertos.o
    0x080060d4   0x00000012   Data   RO         2174    .constdata          stm32f1xx_hal_rcc.o
    0x080060e6   0x00000010   Data   RO         3402    .constdata          system_stm32f1xx.o
    0x080060f6   0x00000008   Data   RO         3403    .constdata          system_stm32f1xx.o
    0x080060fe   0x00000011   Data   RO         5225    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800610f   0x00000001   PAD
    0x08006110   0x00000037   Data   RO          419    .conststring        guitar.o
    0x08006147   0x00000001   PAD
    0x08006148   0x00000052   Data   RO          706    .conststring        freertos.o
    0x0800619a   0x00000002   PAD
    0x0800619c   0x00000020   Data   RO         5655    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00002d60, Max: 0x00005000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0000001c   Data   RW          420    .data               guitar.o
    0x2000001c   0x00000018   Data   RW          707    .data               freertos.o
    0x20000034   0x0000000c   Data   RW         2011    .data               stm32f1xx_hal.o
    0x20000040   0x00000004   Data   RW         3404    .data               system_stm32f1xx.o
    0x20000044   0x0000003c   Data   RW         4068    .data               tasks.o
    0x20000080   0x00000014   Data   RW         4399    .data               timers.o
    0x20000094   0x00000004   Data   RW         4624    .data               cmsis_os2.o
    0x20000098   0x00000020   Data   RW         5070    .data               heap_4.o
    0x200000b8   0x0000000c   Data   RW         5126    .data               port.o
    0x200000c4   0x00000481   Zero   RW           50    .bss                oled.o
    0x20000545   0x00000003   PAD
    0x20000548   0x00000078   Zero   RW          417    .bss                guitar.o
    0x200005c0   0x00000054   Zero   RW          763    .bss                i2c.o
    0x20000614   0x00000048   Zero   RW          806    .bss                tim.o
    0x2000065c   0x00000048   Zero   RW          941    .bss                stm32f1xx_hal_timebase_tim.o
    0x200006a4   0x00000040   Zero   RW         3643    .bss                queue.o
    0x200006e4   0x000004c4   Zero   RW         4067    .bss                tasks.o
    0x20000ba8   0x00000118   Zero   RW         4398    .bss                timers.o
    0x20000cc0   0x000006b8   Zero   RW         4623    .bss                cmsis_os2.o
    0x20001378   0x00001388   Zero   RW         5069    .bss                heap_4.o
    0x20002700   0x00000060   Zero   RW         5474    .bss                c_w.l(libspace.o)
    0x20002760   0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x20002960   0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       868         30          0          4       1720      62246   cmsis_os2.o
         0          0          0          0          0      19436   event_groups.o
       408         70        254         24          0       4305   freertos.o
       148         14          0          0          0        895   gpio.o
      1702        280        143         28        120      22177   guitar.o
       468         22          0         32       5000       4319   heap_4.o
       156         26          0          0         84       1469   i2c.o
       138          0          0          0          0       3010   list.o
       156          6          0          0          0       2244   main.o
       952         78          0          0       1153     431501   oled.o
         0          0       2090          0          0        910   oled_data.o
       662         70          0         12          0      10265   port.o
      2306         24          0          0         64      21647   queue.o
        64         26        236          0       1536        776   startup_stm32f103xb.o
       100         18          0         12          0       4970   stm32f1xx_hal.o
       158         14          0          0          0      27678   stm32f1xx_hal_cortex.o
       516         34          0          0          0       3508   stm32f1xx_hal_gpio.o
      1762         42          0          0          0       8793   stm32f1xx_hal_i2c.o
        72         10          0          0          0        802   stm32f1xx_hal_msp.o
      1304         94         18          0          0       5197   stm32f1xx_hal_rcc.o
      1738         56          0          0          0      14270   stm32f1xx_hal_tim.o
       192         12          0          0          0       2909   stm32f1xx_hal_tim_ex.o
       152         20          0          0         72       1327   stm32f1xx_hal_timebase_tim.o
        24          6          0          0          0       2783   stm32f1xx_it.o
         0          0          0          0          0        388   stream_buffer.o
         2          0         24          4          0       1011   system_stm32f1xx.o
      2384        198          0         60       1220      26397   tasks.o
       320         30          0          0         72       2144   tim.o
       972         80          0         20        280      30759   timers.o

    ----------------------------------------------------------------------
     17768       <USER>       <GROUP>        196      11324     718136   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        44          0          3          0          3          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        44          6          0          0          0         84   __2sprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        10          0          0          0          0         68   _sputc.o
        16          0          0          0          0         68   aeabi_memset.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        24          0          0          0          0         68   strcat.o
        72          0          0          0          0         80   strcpy.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       804         16          0          0          0        272   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
       688        140          0          0          0        208   ddiv.o
        90          4          0          0          0         92   dfixu.o
        38          0          0          0          0         68   dflt_clz.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       430          8          0          0          0        168   faddsub_clz.o
        62          4          0          0          0         84   ffixu.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o

    ----------------------------------------------------------------------
      4434        <USER>         <GROUP>          0         96       3320   Library Totals
        10          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1640         48         17          0         96       1752   c_w.l
      2784        188          0          0          0       1568   fz_ws.l

    ----------------------------------------------------------------------
      4434        <USER>         <GROUP>          0         96       3320   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     22202       1496       2818        196      11420     710012   Grand Totals
     22202       1496       2818        196      11420     710012   ELF Image Totals
     22202       1496       2818        196          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25020 (  24.43kB)
    Total RW  Size (RW Data + ZI Data)             11616 (  11.34kB)
    Total ROM Size (Code + RO Data + RW Data)      25216 (  24.63kB)

==============================================================================

