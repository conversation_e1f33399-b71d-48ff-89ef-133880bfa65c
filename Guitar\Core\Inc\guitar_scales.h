#ifndef __GUITAR_SCALES_H
#define __GUITAR_SCALES_H

#include "guitar.h"

/* 音阶类型 */
typedef enum {
    SCALE_MAJOR = 0,      // 大调
    SCALE_MINOR,          // 小调
    SCALE_PENTATONIC,     // 五声音阶
    SCALE_BLUES,          // 布鲁斯音阶
    SCALE_DORIAN,         // 多利亚调式
    SCALE_CHROMATIC       // 半音阶
} ScaleType_t;

/* 音阶定义 */
typedef struct {
    char name[16];        // 音阶名称
    uint8_t intervals[8]; // 音程关系 (半音数)
    uint8_t noteCount;    // 音符数量
    ScaleType_t type;     // 音阶类型
} Scale_t;

/* 练习模式 */
typedef enum {
    PRACTICE_SCALE = 0,   // 音阶练习
    PRACTICE_ARPEGGIO,    // 琶音练习
    PRACTICE_INTERVAL,    // 音程练习
    PRACTICE_CHORD_TONE,  // 和弦音练习
    PRACTICE_IMPROVISE    // 即兴练习
} PracticeMode_t;

/* 练习统计 */
typedef struct {
    uint32_t practiceTime;    // 练习时间 (秒)
    uint16_t correctNotes;    // 正确音符数
    uint16_t totalNotes;      // 总音符数
    uint8_t currentLevel;     // 当前级别
    uint8_t accuracy;         // 准确率 (%)
    uint32_t lastPracticeTime; // 上次练习时间
} PracticeStats_t;

/* 音阶练习系统 */
typedef struct {
    PracticeMode_t mode;      // 练习模式
    uint8_t currentScale;     // 当前音阶
    uint8_t rootNote;         // 根音
    uint8_t currentNote;      // 当前练习音符
    uint8_t isActive;         // 是否激活
    uint8_t tempo;            // 练习速度
    uint8_t direction;        // 练习方向 (上行/下行)
} ScalePractice_t;

/* 全局变量 */
extern const Scale_t scales[];
extern const uint8_t scaleCount;
extern ScalePractice_t scalePractice;
extern PracticeStats_t practiceStats;

/* 函数声明 */
void Guitar_InitScaleSystem(void);
void Guitar_PlayScale(uint8_t scaleIndex, uint8_t rootNote, uint8_t octave);
void Guitar_StartScalePractice(uint8_t scaleIndex, uint8_t rootNote);
void Guitar_StopScalePractice(void);
void Guitar_NextPracticeNote(void);
uint8_t Guitar_CheckPracticeNote(uint8_t playedNote);

/* 智能练习建议 */
void Guitar_SuggestPractice(void);
void Guitar_GenerateExercise(PracticeMode_t mode, uint8_t difficulty);

/* 音程练习 */
void Guitar_PlayInterval(uint8_t note1, uint8_t note2, uint8_t intervalType);
uint8_t Guitar_RecognizeInterval(uint8_t note1, uint8_t note2);

#endif /* __GUITAR_SCALES_H */
