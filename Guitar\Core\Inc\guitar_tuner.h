#ifndef __GUITAR_TUNER_H
#define __GUITAR_TUNER_H

#include "guitar.h"

/* 调音模式 */
typedef enum {
    TUNE_MODE_CHROMATIC = 0, // 半音调音器
    TUNE_MODE_GUITAR,        // 吉他标准调音
    TUNE_MODE_CUSTOM         // 自定义调音
} TuneMode_t;

/* 音符检测结果 */
typedef struct {
    uint16_t frequency;      // 检测到的频率
    uint8_t noteIndex;       // 音符索引 (0-11, C到B)
    int16_t centsDiff;       // 音分差异 (-50 到 +50)
    uint8_t confidence;      // 检测置信度 (0-100)
    char noteName[4];        // 音符名称 (如 "A4")
} NoteDetection_t;

/* 调音器状态 */
typedef struct {
    TuneMode_t mode;         // 调音模式
    uint8_t isActive;        // 是否激活
    uint8_t targetNote;      // 目标音符
    uint8_t sensitivity;     // 灵敏度 (1-10)
    uint32_t sampleRate;     // 采样率
    uint8_t stabilityCounter; // 稳定性计数器
} TunerState_t;

/* 标准吉他调音 */
extern const uint16_t standardTuning[6]; // E A D G B E

/* 全局变量 */
extern TunerState_t tunerState;
extern NoteDetection_t lastDetection;

/* 函数声明 */
void Guitar_InitTuner(void);
void Guitar_StartTuner(TuneMode_t mode);
void Guitar_StopTuner(void);
NoteDetection_t Guitar_DetectNote(uint16_t frequency);
void Guitar_UpdateTunerDisplay(void);

/* 频率检测 */
uint16_t Guitar_MeasureFrequency(void);
uint8_t Guitar_FrequencyToNote(uint16_t frequency);
int16_t Guitar_CalculateCents(uint16_t frequency, uint16_t targetFreq);

/* 调音辅助 */
void Guitar_PlayReferenceNote(uint8_t noteIndex);
void Guitar_ShowTuningGuide(uint8_t stringIndex);
uint8_t Guitar_IsInTune(uint16_t frequency, uint16_t target, uint8_t tolerance);

/* 视觉指示器 */
void Guitar_DrawTunerNeedle(int16_t centsDiff);
void Guitar_DrawFrequencySpectrum(void);

#endif /* __GUITAR_TUNER_H */
