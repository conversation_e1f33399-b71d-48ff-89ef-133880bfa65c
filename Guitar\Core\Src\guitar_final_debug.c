#include "guitar.h"
#include "tim.h"
#include "OLED.h"
#include <stdio.h>

/**
 * @brief 最终调试 - 彻底检查所有可能的问题
 */

/**
 * @brief 显示当前PWM寄存器状态
 */
void Guitar_ShowPWMRegisters(void)
{
    char buffer[32];
    
    OLED_Clear();
    OLED_ShowString(0, 0, "PWM Registers", OLED_8X16);
    
    sprintf(buffer, "ARR: %lu", htim1.Instance->ARR);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    
    sprintf(buffer, "CCR1: %lu", htim1.Instance->CCR1);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    sprintf(buffer, "CNT: %lu", htim1.Instance->CNT);
    OLED_ShowString(0, 36, buffer, OLED_6X8);
    
    sprintf(buffer, "CR1: 0x%lX", htim1.Instance->CR1);
    OLED_ShowString(0, 46, buffer, OLED_6X8);
    
    sprintf(buffer, "CCER: 0x%lX", htim1.Instance->CCER);
    OLED_ShowString(0, 56, buffer, OLED_6X8);
    
    OLED_Update();
}

/**
 * @brief 手动设置不同频率并显示寄存器值
 */
void Guitar_ManualFrequencyTest(void)
{
    uint16_t testFreqs[] = {200, 400, 800, 1600};
    
    for (uint8_t i = 0; i < 4; i++) {
        uint32_t period = 1000000 / testFreqs[i];
        
        // 直接设置寄存器
        htim1.Instance->ARR = period - 1;
        htim1.Instance->CCR1 = period / 2;
        htim1.Instance->EGR = TIM_EGR_UG;
        
        char buffer[32];
        OLED_Clear();
        OLED_ShowString(0, 0, "Manual Test", OLED_8X16);
        
        sprintf(buffer, "Freq: %dHz", testFreqs[i]);
        OLED_ShowString(0, 16, buffer, OLED_6X8);
        
        sprintf(buffer, "Period: %lu", period);
        OLED_ShowString(0, 26, buffer, OLED_6X8);
        
        sprintf(buffer, "ARR: %lu", htim1.Instance->ARR);
        OLED_ShowString(0, 36, buffer, OLED_6X8);
        
        sprintf(buffer, "CCR1: %lu", htim1.Instance->CCR1);
        OLED_ShowString(0, 46, buffer, OLED_6X8);
        
        OLED_Update();
        HAL_Delay(3000);
        
        // 停止
        htim1.Instance->CCR1 = 0;
        HAL_Delay(500);
    }
}

/**
 * @brief 检查定时器是否真的在运行
 */
void Guitar_CheckTimerRunning(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Timer Check", OLED_8X16);
    
    // 读取计数器值两次，看是否在变化
    uint32_t cnt1 = htim1.Instance->CNT;
    HAL_Delay(10);
    uint32_t cnt2 = htim1.Instance->CNT;
    
    char buffer[32];
    sprintf(buffer, "CNT1: %lu", cnt1);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    
    sprintf(buffer, "CNT2: %lu", cnt2);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    if (cnt1 != cnt2) {
        OLED_ShowString(0, 36, "Timer RUNNING", OLED_6X8);
    } else {
        OLED_ShowString(0, 36, "Timer STOPPED!", OLED_6X8);
    }
    
    // 检查使能位
    if (htim1.Instance->CR1 & TIM_CR1_CEN) {
        OLED_ShowString(0, 46, "CEN: Enabled", OLED_6X8);
    } else {
        OLED_ShowString(0, 46, "CEN: Disabled!", OLED_6X8);
    }
    
    // 检查PWM通道使能
    if (htim1.Instance->CCER & TIM_CCER_CC1E) {
        OLED_ShowString(0, 56, "CH1: Enabled", OLED_6X8);
    } else {
        OLED_ShowString(0, 56, "CH1: Disabled!", OLED_6X8);
    }
    
    OLED_Update();
    HAL_Delay(3000);
}

/**
 * @brief 强制启动PWM（如果没有启动的话）
 */
void Guitar_ForceStartPWM(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Force Start PWM", OLED_8X16);
    OLED_Update();
    
    // 强制启动PWM
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
    
    // 设置一个测试频率
    uint32_t period = 1000000 / 440; // A4
    htim1.Instance->ARR = period - 1;
    htim1.Instance->CCR1 = period / 2;
    htim1.Instance->EGR = TIM_EGR_UG;
    
    HAL_Delay(2000);
    
    // 停止
    htim1.Instance->CCR1 = 0;
}

/**
 * @brief 对比备份工程的方法
 */
void Guitar_BackupMethodTest(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Backup Method", OLED_8X16);
    OLED_Update();
    
    // 完全按照备份工程的方法
    uint16_t notes[] = {262, 294, 330, 349, 392, 440};
    
    for (uint8_t i = 0; i < 6; i++) {
        uint32_t period = 1000000 / notes[i];
        
        if (period > 65535) period = 65535;
        if (period < 100) period = 100;
        
        __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2);
        
        char buffer[32];
        sprintf(buffer, "Note: %dHz", notes[i]);
        OLED_ShowString(0, 16, buffer, OLED_6X8);
        
        sprintf(buffer, "Period: %lu", period);
        OLED_ShowString(0, 26, buffer, OLED_6X8);
        OLED_Update();
        
        HAL_Delay(1500);
        
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);
        HAL_Delay(300);
    }
}

/**
 * @brief 极端频率差异测试
 */
void Guitar_ExtremeFreqTest(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Extreme Test", OLED_8X16);
    OLED_Update();
    
    // 测试极端频率差异
    uint16_t extremeFreqs[] = {100, 2000};
    const char* names[] = {"100Hz", "2000Hz"};
    
    for (uint8_t i = 0; i < 2; i++) {
        uint32_t period = 1000000 / extremeFreqs[i];
        
        htim1.Instance->ARR = period - 1;
        htim1.Instance->CCR1 = period / 2;
        htim1.Instance->EGR = TIM_EGR_UG;
        
        OLED_ShowString(0, 16, names[i], OLED_8X16);
        OLED_Update();
        
        HAL_Delay(3000);
        
        htim1.Instance->CCR1 = 0;
        HAL_Delay(1000);
    }
}

/**
 * @brief 完整的最终诊断序列
 */
void Guitar_FinalDiagnostic(void)
{
    // 1. 检查定时器是否运行
    Guitar_CheckTimerRunning();
    
    // 2. 强制启动PWM
    Guitar_ForceStartPWM();
    
    // 3. 显示PWM寄存器状态
    Guitar_ShowPWMRegisters();
    HAL_Delay(2000);
    
    // 4. 手动频率测试
    Guitar_ManualFrequencyTest();
    
    // 5. 备份工程方法测试
    Guitar_BackupMethodTest();
    
    // 6. 极端频率测试
    Guitar_ExtremeFreqTest();
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Diagnostic", OLED_8X16);
    OLED_ShowString(0, 16, "Complete!", OLED_8X16);
    OLED_Update();
}
