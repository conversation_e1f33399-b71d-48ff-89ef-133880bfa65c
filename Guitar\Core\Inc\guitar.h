#ifndef __GUITAR_H
#define __GUITAR_H

#include "main.h"
#include "tim.h"
#include "cmsis_os.h"

/* 音符频率定义 - 精确的标准音高 (基于A4=440Hz的十二平均律) */
#define NOTE_C4     261.6256f   // Do (C4) - 精确值
#define NOTE_D4     293.6648f   // Re (D4) - 精确值
#define NOTE_E4     329.6276f   // Mi (E4) - 精确值
#define NOTE_F4     349.2282f   // Fa (F4) - 精确值
#define NOTE_G4     391.9954f   // So (G4) - 精确值
#define NOTE_A4     440.0000f   // La (A4) - 标准音
#define NOTE_B4     493.8833f   // Si (B4) - 精确值
#define NOTE_C5     523.2511f   // 高音Do (C5) - 精确值

/* 扩展音符定义 - 用于更完整的音阶 */
#define NOTE_CS4    277.1826f   // C# (升C4)
#define NOTE_DS4    311.1270f   // D# (升D4)
#define NOTE_FS4    369.9944f   // F# (升F4)
#define NOTE_GS4    415.3047f   // G# (升G4)
#define NOTE_AS4    466.1638f   // A# (升A4)

/* 按键事件类型 */
typedef enum {
    KEY_EVENT_PRESS = 0,      // 按键按下
    KEY_EVENT_RELEASE = 1,    // 按键释放
    KEY_EVENT_HOLD = 2        // 按键长按
} KeyEventType_t;

/* 按键事件结构体 */
typedef struct {
    uint8_t keyIndex;         // 按键索引 (0-5)
    KeyEventType_t eventType; // 事件类型
    uint32_t timestamp;       // 时间戳
} KeyEvent_t;

/* 音频命令结构体 */
typedef struct {
    uint16_t frequency;       // 频率 (Hz)
    uint16_t duration;        // 持续时间 (ms)
    uint8_t volume;           // 音量 (0-100)
} AudioCommand_t;

/* 吉他模式 */
typedef enum {
    GUITAR_MODE_SINGLE = 0,   // 单音模式
    GUITAR_MODE_CHORD = 1,    // 和弦模式
    GUITAR_MODE_SCALE = 2     // 音阶模式
} GuitarMode_t;

/* 吉他状态 */
typedef struct {
    GuitarMode_t mode;        // 当前模式
    uint8_t octave;           // 八度 (3-6)
    uint8_t volume;           // 音量 (0-100)
    uint8_t keyPressed[6];    // 按键状态
    uint16_t currentNote;     // 当前音符频率
    uint32_t noteDuration;    // 音符持续时间
} GuitarStatus_t;

/* 外部变量声明 */
extern GuitarStatus_t guitarStatus;
extern osMessageQueueId_t KeyEventQueueHandle;
extern osMessageQueueId_t AudioCommandQueueHandle;

/* 函数声明 */
void Guitar_Init(void);
void Guitar_SetFrequency(uint16_t frequency);
void Guitar_SetPreciseFrequency(float frequency);   // 高精度频率设置
void Guitar_FastSwitchFrequency(uint16_t frequency); // 快速切换频率
void Guitar_UltraFastSwitchNote(uint8_t keyIndex);  // 超快速音符切换
void Guitar_PrecomputePeriods(void);                // 预计算周期表
void Guitar_SetVolume(uint8_t volume);
void Guitar_PlayNote(uint16_t frequency, uint16_t duration);
void Guitar_StopNote(void);
uint16_t Guitar_GetNoteFrequency(uint8_t keyIndex);
float Guitar_GetPreciseNoteFrequency(uint8_t keyIndex); // 高精度音符频率获取
void Guitar_ProcessKeyEvent(KeyEvent_t* keyEvent);
void Guitar_FastSwitchFrequency(uint16_t frequency);
void Guitar_PlayTuningA4(void);  // 音准校准函数
void Guitar_DebugFrequencies(void);  // 频率调试函数
void Guitar_TestA4Accuracy(void);    // A4精度测试
void Guitar_PlayCalibrationSequence(void);  // 校准序列播放
void Guitar_TestIntervalAccuracy(void);     // 音程准确性测试

/* 音频输出诊断函数 */
void Guitar_FullAudioDiagnostic(void);      // 完整音频诊断
void Guitar_ShowPWMDiagnostics(void);       // PWM诊断信息
void Guitar_CheckHardwareConnection(void);  // 硬件连接检查

/* 简单增强功能 - 立即可用 */
void Guitar_SimpleVibrato(uint8_t keyIndex, uint8_t enable);
void Guitar_PlaySimpleChord(uint8_t chordType);
void Guitar_SimpleMetronome(uint16_t bpm, uint8_t beats);

/* 按键扫描相关 */
void Guitar_ScanKeys(void);
uint8_t Guitar_IsKeyPressed(uint8_t keyIndex);
void Guitar_CheckKeyCombo(void);  // 按键组合检查

/* 显示相关 */
void Guitar_UpdateDisplay(void);
void Guitar_TestOLED(void); // 添加OLED测试函数

#endif /* __GUITAR_H */
