#ifndef __GUITAR_H
#define __GUITAR_H

#include "main.h"
#include "tim.h"
#include "cmsis_os.h"

/* 音符频率定义 - 备份工程的简单有效版本 */
#define NOTE_C4     262    // Do
#define NOTE_D4     294    // Re
#define NOTE_E4     330    // Mi
#define NOTE_F4     349    // Fa
#define NOTE_G4     392    // So
#define NOTE_A4     440    // La
#define NOTE_B4     494    // Si
#define NOTE_C5     523    // 高音Do

/* 按键事件类型 */
typedef enum {
    KEY_EVENT_PRESS = 0,      // 按键按下
    KEY_EVENT_RELEASE = 1,    // 按键释放
    KEY_EVENT_HOLD = 2        // 按键长按
} KeyEventType_t;

/* 按键事件结构体 */
typedef struct {
    uint8_t keyIndex;         // 按键索引 (0-5)
    KeyEventType_t eventType; // 事件类型
    uint32_t timestamp;       // 时间戳
} KeyEvent_t;

/* 音频命令结构体 */
typedef struct {
    uint16_t frequency;       // 频率 (Hz)
    uint16_t duration;        // 持续时间 (ms)
    uint8_t volume;           // 音量 (0-100)
} AudioCommand_t;

/* 吉他模式 */
typedef enum {
    GUITAR_MODE_SINGLE = 0,   // 单音模式
    GUITAR_MODE_CHORD = 1,    // 和弦模式
    GUITAR_MODE_SCALE = 2     // 音阶模式
} GuitarMode_t;

/* 吉他状态 */
typedef struct {
    GuitarMode_t mode;        // 当前模式
    uint8_t octave;           // 八度 (3-6)
    uint8_t volume;           // 音量 (0-100)
    uint8_t keyPressed[6];    // 按键状态
    uint16_t currentNote;     // 当前音符频率
    uint32_t noteDuration;    // 音符持续时间
} GuitarStatus_t;

/* 外部变量声明 */
extern GuitarStatus_t guitarStatus;
extern osMessageQueueId_t KeyEventQueueHandle;
extern osMessageQueueId_t AudioCommandQueueHandle;

/* 函数声明 */
void Guitar_Init(void);
void Guitar_SetFrequency(uint16_t frequency);
void Guitar_SetPreciseFrequency(float frequency);   // 高精度频率设置
void Guitar_FastSwitchFrequency(uint16_t frequency); // 快速切换频率
void Guitar_UltraFastSwitchNote(uint8_t keyIndex);  // 超快速音符切换
void Guitar_PrecomputePeriods(void);                // 预计算周期表
void Guitar_SetVolume(uint8_t volume);
void Guitar_PlayNote(uint16_t frequency, uint16_t duration);
void Guitar_StopNote(void);
uint16_t Guitar_GetNoteFrequency(uint8_t keyIndex);
float Guitar_GetPreciseNoteFrequency(uint8_t keyIndex); // 高精度音符频率获取
void Guitar_ProcessKeyEvent(KeyEvent_t* keyEvent);
void Guitar_FastSwitchFrequency(uint16_t frequency);
void Guitar_PlayTuningA4(void);  // 音准校准函数
void Guitar_DebugFrequencies(void);  // 频率调试函数
void Guitar_TestA4Accuracy(void);    // A4精度测试
void Guitar_PlayCalibrationSequence(void);  // 校准序列播放
void Guitar_TestIntervalAccuracy(void);     // 音程准确性测试

/* 音频输出诊断函数 */
void Guitar_FullAudioDiagnostic(void);      // 完整音频诊断
void Guitar_ShowPWMDiagnostics(void);       // PWM诊断信息
void Guitar_CheckHardwareConnection(void);  // 硬件连接检查

/* 无源蜂鸣器专用函数 */
void Guitar_BuzzerPlayNote(float frequency);     // 无源蜂鸣器播放音符
void Guitar_BuzzerStop(void);                    // 停止无源蜂鸣器
void Guitar_BuzzerPlayScale(void);               // 播放音阶测试
void Guitar_BuzzerFullTest(void);                // 完整蜂鸣器测试

/* 硬件诊断函数 */
void Guitar_FullHardwareDiagnostic(void);        // 完整硬件诊断
void Guitar_TestExtremeFrequencies(void);        // 测试极端频率差异
void Guitar_CheckBuzzerType(void);               // 检查蜂鸣器类型
void Guitar_TestGPIOOutput(void);                // 测试GPIO输出

/* 最终调试函数 */
void Guitar_FinalDiagnostic(void);               // 最终完整诊断
void Guitar_DirectRegisterTest(void);            // 直接寄存器测试
void Guitar_SimpleScaleTest(void);               // 简单音阶测试

/* 简单增强功能 - 立即可用 */
void Guitar_SimpleVibrato(uint8_t keyIndex, uint8_t enable);
void Guitar_PlaySimpleChord(uint8_t chordType);
void Guitar_SimpleMetronome(uint16_t bpm, uint8_t beats);

/* 按键扫描相关 */
void Guitar_ScanKeys(void);
uint8_t Guitar_IsKeyPressed(uint8_t keyIndex);
void Guitar_CheckKeyCombo(void);  // 按键组合检查

/* 显示相关 */
void Guitar_UpdateDisplay(void);
void Guitar_TestOLED(void); // 添加OLED测试函数

#endif /* __GUITAR_H */
