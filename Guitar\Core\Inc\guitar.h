#ifndef __GUITAR_H
#define __GUITAR_H

#include "main.h"
#include "tim.h"
#include "cmsis_os.h"

/* 音符频率定义 - 精确的标准音高 */
#define NOTE_C4     261.63f    // Do (C4)
#define NOTE_D4     293.66f    // Re (D4)
#define NOTE_E4     329.63f    // Mi (E4)
#define NOTE_F4     349.23f    // Fa (F4)
#define NOTE_G4     392.00f    // So (G4)
#define NOTE_A4     440.00f    // La (A4) - 标准音
#define NOTE_B4     493.88f    // Si (B4)
#define NOTE_C5     523.25f    // 高音Do (C5)

/* 按键事件类型 */
typedef enum {
    KEY_EVENT_PRESS = 0,      // 按键按下
    KEY_EVENT_RELEASE = 1,    // 按键释放
    KEY_EVENT_HOLD = 2        // 按键长按
} KeyEventType_t;

/* 按键事件结构体 */
typedef struct {
    uint8_t keyIndex;         // 按键索引 (0-5)
    KeyEventType_t eventType; // 事件类型
    uint32_t timestamp;       // 时间戳
} KeyEvent_t;

/* 音频命令结构体 */
typedef struct {
    uint16_t frequency;       // 频率 (Hz)
    uint16_t duration;        // 持续时间 (ms)
    uint8_t volume;           // 音量 (0-100)
} AudioCommand_t;

/* 吉他模式 */
typedef enum {
    GUITAR_MODE_SINGLE = 0,   // 单音模式
    GUITAR_MODE_CHORD = 1,    // 和弦模式
    GUITAR_MODE_SCALE = 2     // 音阶模式
} GuitarMode_t;

/* 吉他状态 */
typedef struct {
    GuitarMode_t mode;        // 当前模式
    uint8_t octave;           // 八度 (3-6)
    uint8_t volume;           // 音量 (0-100)
    uint8_t keyPressed[6];    // 按键状态
    uint16_t currentNote;     // 当前音符频率
    uint32_t noteDuration;    // 音符持续时间
} GuitarStatus_t;

/* 外部变量声明 */
extern GuitarStatus_t guitarStatus;
extern osMessageQueueId_t KeyEventQueueHandle;
extern osMessageQueueId_t AudioCommandQueueHandle;

/* 函数声明 */
void Guitar_Init(void);
void Guitar_SetFrequency(uint16_t frequency);
void Guitar_FastSwitchFrequency(uint16_t frequency); // 快速切换频率
void Guitar_UltraFastSwitchNote(uint8_t keyIndex);  // 超快速音符切换
void Guitar_PrecomputePeriods(void);                // 预计算周期表
void Guitar_SetVolume(uint8_t volume);
void Guitar_PlayNote(uint16_t frequency, uint16_t duration);
void Guitar_StopNote(void);
uint16_t Guitar_GetNoteFrequency(uint8_t keyIndex);
void Guitar_ProcessKeyEvent(KeyEvent_t* keyEvent);
void Guitar_FastSwitchFrequency(uint16_t frequency);
void Guitar_PlayTuningA4(void);  // 音准校准函数
void Guitar_DebugFrequencies(void);  // 频率调试函数
void Guitar_TestA4Accuracy(void);    // A4精度测试

/* 简单增强功能 - 立即可用 */
void Guitar_SimpleVibrato(uint8_t keyIndex, uint8_t enable);
void Guitar_PlaySimpleChord(uint8_t chordType);
void Guitar_SimpleMetronome(uint16_t bpm, uint8_t beats);

/* 按键扫描相关 */
void Guitar_ScanKeys(void);
uint8_t Guitar_IsKeyPressed(uint8_t keyIndex);
void Guitar_CheckKeyCombo(void);  // 按键组合检查

/* 显示相关 */
void Guitar_UpdateDisplay(void);
void Guitar_TestOLED(void); // 添加OLED测试函数

#endif /* __GUITAR_H */
