{"version": "2.0.0", "tasks": [{"label": "Guitar Build Test", "type": "shell", "command": "arm-none-eabi-gcc", "args": ["-c", "-mcpu=cortex-m3", "-mthumb", "-DUSE_HAL_DRIVER", "-DSTM32F103xB", "-I./Core/Inc", "-I./Drivers/STM32F1xx_HAL_Driver/Inc", "-I./Drivers/CMSIS/Device/ST/STM32F1xx/Include", "-I./Drivers/CMSIS/Include", "-I./Middlewares/Third_Party/FreeRTOS/Source/include", "-I./Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2", "-I./Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM3", "-Wall", "Core/Src/guitar.c", "-o", "guitar.o"], "group": "build", "problemMatcher": ["$gcc"]}]}