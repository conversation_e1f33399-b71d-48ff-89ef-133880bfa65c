#ifndef __GUITAR_EFFECTS_H
#define __GUITAR_EFFECTS_H

#include "guitar.h"

/* 音效类型枚举 */
typedef enum {
    EFFECT_NONE = 0,        // 无音效
    EFFECT_VIBRATO,         // 颤音
    EFFECT_TREMOLO,         // 震音
    EFFECT_BEND,            // 弯音
    EFFECT_SLIDE,           // 滑音
    EFFECT_HAMMER_ON,       // 击弦
    EFFECT_PULL_OFF,        // 拉弦
    EFFECT_STACCATO,        // 断奏
    EFFECT_LEGATO           // 连奏
} AudioEffect_t;

/* 音效参数结构体 */
typedef struct {
    AudioEffect_t type;     // 音效类型
    uint8_t intensity;      // 强度 (0-100)
    uint16_t duration;      // 持续时间 (ms)
    uint8_t speed;          // 速度 (用于颤音等)
} EffectParams_t;

/* 演奏技巧结构体 */
typedef struct {
    uint8_t sustainEnabled; // 延音开启
    uint8_t vibratoEnabled; // 颤音开启
    uint8_t currentEffect;  // 当前音效
    uint8_t attackTime;     // 起音时间 (ms)
    uint8_t releaseTime;    // 释放时间 (ms)
} PlayingTechnique_t;

/* 全局变量 */
extern PlayingTechnique_t playingTech;

/* 函数声明 */
void Guitar_InitEffects(void);
void Guitar_ApplyEffect(uint16_t baseFreq, EffectParams_t* effect);
void Guitar_PlayVibratoNote(uint16_t frequency, uint16_t duration, uint8_t vibratoSpeed);
void Guitar_PlayBendNote(uint16_t fromFreq, uint16_t toFreq, uint16_t duration);
void Guitar_PlaySlideNote(uint16_t fromFreq, uint16_t toFreq, uint16_t duration);
void Guitar_SetSustain(uint8_t enabled);
void Guitar_SetAttackRelease(uint8_t attackMs, uint8_t releaseMs);

#endif /* __GUITAR_EFFECTS_H */
