#include "guitar_debug.h"
#include "guitar.h"
#include "OLED.h"
#include <stdio.h>

/**
 * @brief 音频输出诊断工具
 * 用于检查PWM参数、频率计算和硬件连接
 */

/**
 * @brief 显示PWM诊断信息
 */
void Guitar_ShowPWMDiagnostics(void)
{
    // 获取系统时钟信息
    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
    uint32_t pclk2 = HAL_RCC_GetPCLK2Freq();
    uint32_t timerClock = pclk2;
    
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }
    
    char buffer[32];
    OLED_Clear();
    OLED_ShowString(0, 0, "PWM Diagnostics", OLED_8X16);
    
    sprintf(buffer, "SYSCLK:%luMHz", sysclk/1000000);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    
    sprintf(buffer, "PCLK2:%luMHz", pclk2/1000000);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    sprintf(buffer, "TIM1CLK:%luMHz", timerClock/1000000);
    OLED_ShowString(0, 36, buffer, OLED_6X8);
    
    // 显示当前PWM设置
    uint32_t arr = __HAL_TIM_GET_AUTORELOAD(&htim1);
    uint32_t ccr = __HAL_TIM_GET_COMPARE(&htim1, TIM_CHANNEL_1);
    
    sprintf(buffer, "ARR:%lu CCR:%lu", arr, ccr);
    OLED_ShowString(0, 46, buffer, OLED_6X8);
    
    if (arr > 0) {
        uint32_t freq = timerClock / (arr + 1);
        sprintf(buffer, "PWM Freq:%luHz", freq);
        OLED_ShowString(0, 56, buffer, OLED_6X8);
    }
    
    OLED_Update();
}

/**
 * @brief 测试所有音符的PWM参数
 */
void Guitar_TestAllNotePWM(void)
{
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }
    
    const char* noteNames[6] = {"C4", "D4", "E4", "F4", "G4", "A4"};
    
    for (uint8_t i = 0; i < 6; i++) {
        float targetFreq = Guitar_GetPreciseNoteFrequency(i);
        double precisePeriod = (double)timerClock / (double)targetFreq;
        uint32_t period = (uint32_t)(precisePeriod + 0.5);
        double actualFreq = (double)timerClock / (double)period;
        double error = ((actualFreq - targetFreq) / targetFreq) * 100.0;
        
        char buffer[32];
        OLED_Clear();
        OLED_ShowString(0, 0, "Note Test", OLED_8X16);
        
        sprintf(buffer, "Note: %s", noteNames[i]);
        OLED_ShowString(0, 16, buffer, OLED_6X8);
        
        sprintf(buffer, "Target:%.2fHz", targetFreq);
        OLED_ShowString(0, 26, buffer, OLED_6X8);
        
        sprintf(buffer, "Actual:%.2fHz", actualFreq);
        OLED_ShowString(0, 36, buffer, OLED_6X8);
        
        sprintf(buffer, "Period:%lu", period);
        OLED_ShowString(0, 46, buffer, OLED_6X8);
        
        sprintf(buffer, "Error:%.4f%%", error);
        OLED_ShowString(0, 56, buffer, OLED_6X8);
        
        OLED_Update();
        
        // 播放这个音符3秒
        Guitar_SetPreciseFrequency(targetFreq);
        HAL_Delay(3000);
        Guitar_StopNote();
        HAL_Delay(500);
    }
}

/**
 * @brief 测试频率扫描
 */
void Guitar_FrequencySweep(uint16_t startFreq, uint16_t endFreq, uint16_t stepTime)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Freq Sweep", OLED_8X16);
    
    char buffer[32];
    sprintf(buffer, "%d-%dHz", startFreq, endFreq);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    OLED_Update();
    
    for (uint16_t freq = startFreq; freq <= endFreq; freq += 10) {
        Guitar_SetPreciseFrequency((float)freq);
        
        sprintf(buffer, "Playing:%dHz", freq);
        OLED_ShowString(0, 26, buffer, OLED_6X8);
        OLED_Update();
        
        HAL_Delay(stepTime);
    }
    
    Guitar_StopNote();
}

/**
 * @brief 检查硬件连接
 */
void Guitar_CheckHardwareConnection(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Hardware Check", OLED_8X16);
    
    // 检查PWM输出引脚配置
    GPIO_TypeDef* port = GPIOA;
    uint32_t pin = GPIO_PIN_8;
    
    // 读取引脚配置
    uint32_t moder = (port->CRL >> (8 * 4)) & 0xF; // PA8在CRH中
    uint32_t cnf = (port->CRH >> (8 * 4 + 2)) & 0x3;
    
    char buffer[32];
    sprintf(buffer, "PA8 Mode:%lx", moder);
    OLED_ShowString(0, 16, buffer, OLED_6X8);
    
    sprintf(buffer, "PA8 CNF:%lx", cnf);
    OLED_ShowString(0, 26, buffer, OLED_6X8);
    
    // 检查定时器状态
    if (__HAL_TIM_IS_TIM_COUNTING_DOWN(&htim1)) {
        OLED_ShowString(0, 36, "TIM1: Running", OLED_6X8);
    } else {
        OLED_ShowString(0, 36, "TIM1: Stopped", OLED_6X8);
    }
    
    // 检查PWM通道使能状态
    if (htim1.Instance->CCER & TIM_CCER_CC1E) {
        OLED_ShowString(0, 46, "CH1: Enabled", OLED_6X8);
    } else {
        OLED_ShowString(0, 46, "CH1: Disabled", OLED_6X8);
    }
    
    OLED_Update();
}

/**
 * @brief 生成测试音调序列
 */
void Guitar_PlayTestTones(void)
{
    // 播放标准测试频率
    uint16_t testFreqs[] = {220, 440, 880, 1760}; // A3, A4, A5, A6
    const char* testNames[] = {"A3", "A4", "A5", "A6"};
    
    for (uint8_t i = 0; i < 4; i++) {
        char buffer[32];
        OLED_Clear();
        OLED_ShowString(0, 0, "Test Tone", OLED_8X16);
        
        sprintf(buffer, "%s - %dHz", testNames[i], testFreqs[i]);
        OLED_ShowString(0, 16, buffer, OLED_6X8);
        OLED_Update();
        
        Guitar_SetPreciseFrequency((float)testFreqs[i]);
        HAL_Delay(2000);
        Guitar_StopNote();
        HAL_Delay(500);
    }
}

/**
 * @brief 检查音频输出电路建议
 */
void Guitar_ShowAudioCircuitAdvice(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Audio Circuit", OLED_8X16);
    OLED_ShowString(0, 16, "Required:", OLED_6X8);
    OLED_ShowString(0, 26, "1.Low-pass filter", OLED_6X8);
    OLED_ShowString(0, 36, "2.Audio amplifier", OLED_6X8);
    OLED_ShowString(0, 46, "3.Speaker/Buzzer", OLED_6X8);
    OLED_ShowString(0, 56, "PA8->Filter->Amp", OLED_6X8);
    OLED_Update();
    
    HAL_Delay(5000);
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Simple Circuit:", OLED_8X16);
    OLED_ShowString(0, 16, "PA8--[R]--+", OLED_6X8);
    OLED_ShowString(0, 26, "          |", OLED_6X8);
    OLED_ShowString(0, 36, "         [C]", OLED_6X8);
    OLED_ShowString(0, 46, "          |", OLED_6X8);
    OLED_ShowString(0, 56, "       Speaker", OLED_6X8);
    OLED_Update();
}

/**
 * @brief 完整的音频系统诊断
 */
void Guitar_FullAudioDiagnostic(void)
{
    // 1. 显示PWM诊断信息
    Guitar_ShowPWMDiagnostics();
    HAL_Delay(3000);
    
    // 2. 检查硬件连接
    Guitar_CheckHardwareConnection();
    HAL_Delay(3000);
    
    // 3. 播放测试音调
    Guitar_PlayTestTones();
    
    // 4. 测试所有音符
    Guitar_TestAllNotePWM();
    
    // 5. 频率扫描测试
    Guitar_FrequencySweep(200, 2000, 100);
    
    // 6. 显示电路建议
    Guitar_ShowAudioCircuitAdvice();
}
