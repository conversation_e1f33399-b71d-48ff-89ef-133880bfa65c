#include "guitar.h"
#include "gpio.h"
#include "OLED.h"
#include "tim.h"
#include "performance.h"  // 添加性能监控
#include "core_cm3.h"     // 添加CMSIS核心头文件
#include <string.h>
#include <stdio.h>

/* 全局变量 */
GuitarStatus_t guitarStatus = {
    .mode = GUITAR_MODE_SINGLE,
    .octave = 4,
    .volume = 80,
    .keyPressed = {0},
    .currentNote = 0,
    .noteDuration = 0
};

/* 音符映射表 - 将6个按键映射到不同音符 */
const float noteFrequencies[6] = {
    NOTE_C4,    // KEY1 - Do  (261.63Hz)
    NOTE_D4,    // KEY2 - Re  (293.66Hz)
    NOTE_E4,    // KEY3 - Mi  (329.63Hz)
    NOTE_F4,    // KEY4 - Fa  (349.23Hz)
    NOTE_G4,    // KEY5 - So  (392.00Hz)
    NOTE_A4     // KEY6 - La  (440.00Hz)
};

/* 预计算的PWM周期表 - 提高切换速度 */
static uint32_t precomputedPeriods[6][4]; // [按键][八度]

/**
 * @brief 预计算所有音符的PWM周期值（高精度版本）
 */
void Guitar_PrecomputePeriods(void)
{
    // 动态获取定时器时钟频率
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }

    for (uint8_t key = 0; key < 6; key++) {
        for (uint8_t octave = 0; octave < 4; octave++) {
            // 使用双精度浮点数进行高精度计算
            double baseFreq = (double)noteFrequencies[key];
            double freq;

            switch (octave) {
                case 0: freq = baseFreq / 2.0; break;  // 八度3
                case 1: freq = baseFreq; break;        // 八度4
                case 2: freq = baseFreq * 2.0; break;  // 八度5
                case 3: freq = baseFreq * 4.0; break;  // 八度6
                default: freq = baseFreq; break;
            }

            // 使用高精度计算周期值
            double precisePeriod = (double)timerClock / freq;
            uint32_t period = (uint32_t)(precisePeriod + 0.5); // 四舍五入

            // 边界检查
            if (period > 65535) period = 65535;
            if (period < 100) period = 100;  // 降低最小值限制以支持更高频率

            precomputedPeriods[key][octave] = period;
        }
    }
}

/* 按键防抖相关变量 */
static uint8_t keyLastState[6] = {1, 1, 1, 1, 1, 1}; // 上次按键状态 (1=释放, 0=按下)
static uint32_t keyDebounceTime[6] = {0};             // 按键防抖计时
static uint8_t keyStableState[6] = {1, 1, 1, 1, 1, 1}; // 稳定的按键状态
// static uint8_t currentActiveKey = 255;                // 当前播放的按键 (255表示无) - 暂时未使用

// 优化：使用位运算加速按键状态处理
// static uint8_t keyStateBitmask = 0;                   // 按键状态位掩码，便于快速判断 - 暂时未使用

#define KEY_DEBOUNCE_DELAY 8   // 进一步减少防抖延时到8ms

/**
 * @brief 吉他系统初始化
 */
void Guitar_Init(void)
{
    // 预计算所有PWM周期值
    Guitar_PrecomputePeriods();
    
    // 初始化吉他状态
    guitarStatus.mode = GUITAR_MODE_SINGLE;
    guitarStatus.octave = 4;
    guitarStatus.volume = 80;
    guitarStatus.currentNote = 0;
    guitarStatus.noteDuration = 0;
    memset(guitarStatus.keyPressed, 0, sizeof(guitarStatus.keyPressed));
    
    // 启动PWM输出 (但不发声)
    HAL_TIM_PWM_Start(&htim1, TIM_CHANNEL_1);
    Guitar_StopNote();
}

/**
 * @brief 简单颤音效果 (实用版)
 * @param keyIndex 按键索引
 * @param enable 开启/关闭颤音
 */
void Guitar_SimpleVibrato(uint8_t keyIndex, uint8_t enable)
{
    if (enable && keyIndex < 6) {
        uint16_t baseFreq = Guitar_GetNoteFrequency(keyIndex);
        
        // 简单的颤音：在基频±2%范围内快速变化
        for (uint8_t i = 0; i < 10; i++) {
            uint16_t vibratoFreq = baseFreq + (baseFreq * (i % 4 - 2)) / 100;
            Guitar_FastSwitchFrequency(vibratoFreq);
            HAL_Delay(50); // 50ms间隔
        }
        
        // 恢复原频率
        Guitar_FastSwitchFrequency(baseFreq);
    }
}

/**
 * @brief 简单和弦演奏 (实用版)
 * @param chordType 和弦类型 (0=C大调, 1=Am, 2=F大调, 3=G大调)
 */
void Guitar_PlaySimpleChord(uint8_t chordType)
{
    uint16_t chordNotes[4] = {0};
    
    switch (chordType) {
        case 0: // C大调 (C-E-G)
            chordNotes[0] = NOTE_C4;
            chordNotes[1] = NOTE_E4;
            chordNotes[2] = NOTE_G4;
            break;
        case 1: // Am (A-C-E)  
            chordNotes[0] = NOTE_A4;
            chordNotes[1] = NOTE_C4;
            chordNotes[2] = NOTE_E4;
            break;
        case 2: // F大调 (F-A-C)
            chordNotes[0] = NOTE_F4;
            chordNotes[1] = NOTE_A4;
            chordNotes[2] = NOTE_C5;
            break;
        case 3: // G大调 (G-B-D)
            chordNotes[0] = NOTE_G4;
            chordNotes[1] = NOTE_B4;
            chordNotes[2] = NOTE_D4;
            break;
    }
    
    // 琶音演奏
    for (uint8_t i = 0; i < 3; i++) {
        if (chordNotes[i] > 0) {
            Guitar_FastSwitchFrequency(chordNotes[i]);
            HAL_Delay(200); // 200ms每个音符
        }
    }
}

/**
 * @brief 简单节拍器 (实用版)
 * @param bpm 每分钟节拍数 (60-180)
 * @param beats 节拍数 (0=无限循环)
 */
void Guitar_SimpleMetronome(uint16_t bpm, uint8_t beats)
{
    if (bpm < 60) bpm = 60;
    if (bpm > 180) bpm = 180;
    
    uint32_t interval = 60000 / bpm; // 毫秒间隔
    uint8_t currentBeat = 0;
    
    while (beats == 0 || currentBeat < beats) {
        // 节拍音 (高音C)
        Guitar_FastSwitchFrequency(NOTE_C5);
        HAL_Delay(50); // 50ms节拍音
        
        Guitar_FastSwitchFrequency(0);
        HAL_Delay(interval - 50); // 剩余时间静音
        
        currentBeat++;
        
        // 每4拍一个重音
        if (currentBeat % 4 == 1) {
            // 重音节拍稍长一些
            Guitar_FastSwitchFrequency(NOTE_C5);
            HAL_Delay(80);
            Guitar_FastSwitchFrequency(0);
        }
    }
}

void Guitar_UltraFastSwitchNote(uint8_t keyIndex)
{
    if (keyIndex >= 6) {
        // 停止播放
        htim1.Instance->CCR1 = 0;
        guitarStatus.currentNote = 0;
        return;
    }

    // 获取预计算的周期值
    uint8_t octaveIndex = guitarStatus.octave - 3; // 转换为数组索引
    if (octaveIndex > 3) octaveIndex = 1; // 默认为八度4

    uint32_t period = precomputedPeriods[keyIndex][octaveIndex];

    // 无源蜂鸣器优化：固定50%占空比，忽略音量设置
    uint32_t pulse = period / 2; // 固定50%占空比

    // 超快原子更新 - 使用HAL库的中断控制
    __disable_irq();

    htim1.Instance->ARR = period - 1;
    htim1.Instance->CCR1 = pulse;
    htim1.Instance->EGR = TIM_EGR_UG;

    __enable_irq(); // 恢复中断状态

    // 使用高精度频率更新当前音符状态
    float preciseFreq = Guitar_GetPreciseNoteFrequency(keyIndex);
    guitarStatus.currentNote = (uint16_t)(preciseFreq + 0.5f);
}

/**
 * @brief 设置PWM频率产生音调（无源蜂鸣器优化版本）
 * @param frequency 频率值 (Hz) - 支持浮点数输入
 */
void Guitar_SetFrequency(uint16_t frequency)
{
    if (frequency == 0) {
        Guitar_StopNote();
        return;
    }

    // 动态获取定时器时钟频率，不假设固定值
    // TIM1在APB2上，时钟 = APB2时钟 (当Prescaler=0时)
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();

    // 如果APB2分频器不是1，定时器时钟会翻倍
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }

    // 高精度计算PWM周期值
    // 使用双精度浮点数提高计算精度
    double preciseFreq = (double)frequency;
    double precisePeriod = (double)timerClock / preciseFreq;
    uint32_t period = (uint32_t)(precisePeriod + 0.5); // 四舍五入

    // 边界检查
    if (period > 65535) period = 65535; // 限制最大值(16位定时器)
    if (period < 100) period = 100;     // 限制最小值(避免频率过高)

    // 计算实际频率用于验证
    double actualFreq = (double)timerClock / (double)period;
    double freqError = ((actualFreq - preciseFreq) / preciseFreq) * 100.0; // 误差百分比

    // 无源蜂鸣器优化：固定50%占空比，只改变频率
    // 无源蜂鸣器不响应占空比变化，只响应频率变化
    __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2); // 固定50%占空比

    // 强制更新定时器 - 使用更兼容的方法
    htim1.Instance->EGR = TIM_EGR_UG;

    guitarStatus.currentNote = frequency;
}

/**
 * @brief 设置音量 (无源蜂鸣器版本 - 通过PWM开关控制)
 * @param volume 音量值 0-100
 */
void Guitar_SetVolume(uint8_t volume)
{
    if (volume > 100) volume = 100;

    guitarStatus.volume = volume;

    // 无源蜂鸣器音量控制：
    // 方法1：简单开关控制（推荐）
    if (volume == 0) {
        // 音量为0时停止PWM输出
        __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0);
    } else {
        // 音量不为0时恢复50%占空比
        if (guitarStatus.currentNote > 0) {
            uint32_t period = __HAL_TIM_GET_AUTORELOAD(&htim1) + 1;
            __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2);
        }
    }

    // 注意：无源蜂鸣器不能通过占空比调节音量
    // 如需音量控制，可考虑：
    // 1. 硬件音量控制电路
    // 2. PWM间歇输出（脉冲调制）
    // 3. 更换为有源蜂鸣器或扬声器
}

/**
 * @brief 音准测试函数 - 播放标准A4音符（高精度版本）
 */
void Guitar_PlayTuningA4(void)
{
    // 播放精确的标准A4 (440.0Hz) 用于音准校准
    Guitar_SetPreciseFrequency(NOTE_A4);
}

/**
 * @brief 高精度音符精度测试 - 验证所有音符的准确性
 */
void Guitar_DebugFrequencies(void)
{
    // 获取实际时钟频率用于调试
    uint32_t sysclk = HAL_RCC_GetSysClockFreq();
    uint32_t pclk2 = HAL_RCC_GetPCLK2Freq();
    uint32_t timerClock = pclk2;

    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }

    // 这个函数可以用于调试，输出实际计算的频率
    // 可以通过OLED显示或串口输出以下信息：
    // printf("SYSCLK: %lu Hz\n", sysclk);
    // printf("PCLK2: %lu Hz\n", pclk2);
    // printf("Timer Clock: %lu Hz\n", timerClock);
    // printf("=== 音符精度测试 ===\n");

    for (uint8_t i = 0; i < 6; i++) {
        float targetFreq = Guitar_GetPreciseNoteFrequency(i);
        double precisePeriod = (double)timerClock / (double)targetFreq;
        uint32_t period = (uint32_t)(precisePeriod + 0.5);
        double actualFreq = (double)timerClock / (double)period;
        double error = ((actualFreq - targetFreq) / targetFreq) * 100.0;

        // 可以通过串口或显示屏输出频率值进行验证
        // printf("Key %d: Target=%.4fHz, Period=%lu, Actual=%.4fHz, Error=%.4f%%\n",
        //        i+1, targetFreq, period, actualFreq, error);
    }
}

/**
 * @brief 高精度A4音准测试 - 验证440Hz的准确性
 */
void Guitar_TestA4Accuracy(void)
{
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }

    // 使用高精度计算
    double target_freq = 440.0;
    double precise_period = (double)timerClock / target_freq;
    uint32_t period_440 = (uint32_t)(precise_period + 0.5);
    double actual_freq = (double)timerClock / (double)period_440;
    double error_hz = actual_freq - target_freq;
    double error_percent = (error_hz / target_freq) * 100.0;

    // 播放计算出的A4频率
    __HAL_TIM_SET_AUTORELOAD(&htim1, period_440 - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period_440 / 2);
    htim1.Instance->EGR = TIM_EGR_UG;

    // 实际频率应该非常接近440Hz (误差 < 0.01Hz)
    // printf("A4 测试: 目标=440.0000Hz, 实际=%.4fHz, 误差=%.4fHz (%.6f%%)\n",
    //        actual_freq, error_hz, error_percent);
}

/**
 * @brief 音调校准测试序列 - 播放所有音符进行校准
 */
void Guitar_PlayCalibrationSequence(void)
{
    // 播放所有6个音符，每个持续1秒
    for (uint8_t i = 0; i < 6; i++) {
        float freq = Guitar_GetPreciseNoteFrequency(i);
        Guitar_SetPreciseFrequency(freq);
        HAL_Delay(1000); // 1秒
        Guitar_StopNote();
        HAL_Delay(200);  // 间隔200ms
    }
}

/**
 * @brief 音程准确性测试 - 测试音符间的频率比例
 */
void Guitar_TestIntervalAccuracy(void)
{
    // 测试完全五度 (C4 到 G4, 应该是 3:2 的比例)
    float c4_freq = noteFrequencies[0]; // C4
    float g4_freq = noteFrequencies[4]; // G4
    double ratio = (double)g4_freq / (double)c4_freq;
    double expected_ratio = 1.498307; // 精确的完全五度比例
    double ratio_error = ((ratio - expected_ratio) / expected_ratio) * 100.0;

    // printf("完全五度测试 (C4-G4): 实际比例=%.6f, 期望比例=%.6f, 误差=%.4f%%\n",
    //        ratio, expected_ratio, ratio_error);

    // 测试大三度 (C4 到 E4, 应该是 5:4 的比例)
    float e4_freq = noteFrequencies[2]; // E4
    double major_third_ratio = (double)e4_freq / (double)c4_freq;
    double expected_major_third = 1.259921; // 精确的大三度比例
    double major_third_error = ((major_third_ratio - expected_major_third) / expected_major_third) * 100.0;

    // printf("大三度测试 (C4-E4): 实际比例=%.6f, 期望比例=%.6f, 误差=%.4f%%\n",
    //        major_third_ratio, expected_major_third, major_third_error);
}

/**
 * @brief 快速切换频率（用于特殊功能）
 * @param frequency 频率 (Hz, 0为停止)
 */
void Guitar_FastSwitchFrequency(uint16_t frequency)
{
    Guitar_SetFrequency(frequency);
}

/**
 * @brief 播放指定频率和时长的音符
 * @param frequency 频率 (Hz)  
 * @param duration 持续时间 (ms)
 */
void Guitar_PlayNote(uint16_t frequency, uint16_t duration)
{
    Guitar_SetFrequency(frequency);
    Guitar_SetVolume(guitarStatus.volume);
    guitarStatus.noteDuration = HAL_GetTick() + duration;
}

/**
 * @brief 停止音符播放
 */
void Guitar_StopNote(void)
{
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, 0); // 占空比设为0
    guitarStatus.currentNote = 0;
    guitarStatus.noteDuration = 0;
}

/**
 * @brief 设置高精度PWM频率（支持浮点数输入）
 * @param frequency 精确频率值 (Hz) - 浮点数
 */
void Guitar_SetPreciseFrequency(float frequency)
{
    if (frequency <= 0.0f) {
        Guitar_StopNote();
        return;
    }

    // 动态获取定时器时钟频率
    uint32_t timerClock = HAL_RCC_GetPCLK2Freq();
    if ((RCC->CFGR & RCC_CFGR_PPRE2) != RCC_CFGR_PPRE2_DIV1) {
        timerClock *= 2;
    }

    // 使用双精度浮点数进行高精度计算
    double preciseFreq = (double)frequency;
    double precisePeriod = (double)timerClock / preciseFreq;
    uint32_t period = (uint32_t)(precisePeriod + 0.5); // 四舍五入

    // 边界检查
    if (period > 65535) period = 65535;
    if (period < 100) period = 100;

    // 立即更新PWM设置
    __HAL_TIM_SET_AUTORELOAD(&htim1, period - 1);
    __HAL_TIM_SET_COMPARE(&htim1, TIM_CHANNEL_1, period / 2);
    htim1.Instance->EGR = TIM_EGR_UG;

    guitarStatus.currentNote = (uint16_t)frequency;
}

/**
 * @brief 根据按键索引获取对应的精确音符频率
 * @param keyIndex 按键索引 (0-5)
 * @return 精确音符频率 (浮点数)
 */
float Guitar_GetPreciseNoteFrequency(uint8_t keyIndex)
{
    if (keyIndex >= 6) return 0.0f;

    // 根据八度调整频率，使用双精度浮点数计算
    double baseFreq = (double)noteFrequencies[keyIndex];
    double resultFreq;

    switch (guitarStatus.octave) {
        case 3: resultFreq = baseFreq / 2.0;       // 低八度
                break;
        case 4: resultFreq = baseFreq;             // 标准八度
                break;
        case 5: resultFreq = baseFreq * 2.0;       // 高八度
                break;
        case 6: resultFreq = baseFreq * 4.0;       // 超高八度
                break;
        default: resultFreq = baseFreq;
                break;
    }

    return (float)resultFreq;
}

/**
 * @brief 根据按键索引获取对应的音符频率（兼容性函数）
 * @param keyIndex 按键索引 (0-5)
 * @return 音符频率
 */
uint16_t Guitar_GetNoteFrequency(uint8_t keyIndex)
{
    float preciseFreq = Guitar_GetPreciseNoteFrequency(keyIndex);
    return (uint16_t)(preciseFreq + 0.5f); // 四舍五入到最近整数
}

/**
 * @brief 检查指定按键是否被按下
 * @param keyIndex 按键索引 (0-5)
 * @return 1-按下, 0-释放
 */
uint8_t Guitar_IsKeyPressed(uint8_t keyIndex)
{
    GPIO_TypeDef* gpioPort = GPIOA;
    uint16_t gpioPin;
    
    switch (keyIndex) {
        case 0: gpioPin = KEY1_Pin; break;
        case 1: gpioPin = KEY2_Pin; break;
        case 2: gpioPin = KEY3_Pin; break;
        case 3: gpioPin = KEY4_Pin; break;
        case 4: gpioPin = KEY5_Pin; break;
        case 5: gpioPin = KEY6_Pin; break;
        default: return 0;
    }
    
    // 读取GPIO状态 (按下为低电平)
    return (HAL_GPIO_ReadPin(gpioPort, gpioPin) == GPIO_PIN_RESET) ? 1 : 0;
}

/**
 * @brief 扫描所有按键状态并立即处理音频（超快响应版）
 */
void Guitar_ScanKeys(void)
{
    uint32_t currentTime = HAL_GetTick();
    uint8_t anyKeyPressed = 0;
    uint8_t highestPriorityKey = 255;
    
    for (uint8_t i = 0; i < 6; i++) {
        uint8_t currentState = Guitar_IsKeyPressed(i);
        
        // 检测状态变化
        if (currentState != keyLastState[i]) {
            keyDebounceTime[i] = currentTime;
            keyLastState[i] = currentState;
        }
        
        // 防抖处理
        if ((currentTime - keyDebounceTime[i]) >= KEY_DEBOUNCE_DELAY) {
            if (currentState != keyStableState[i]) {
                keyStableState[i] = currentState;
                guitarStatus.keyPressed[i] = currentState;
                
                // 可选：仍然发送事件供显示任务使用
                KeyEvent_t keyEvent;
                keyEvent.keyIndex = i;
                keyEvent.eventType = currentState ? KEY_EVENT_PRESS : KEY_EVENT_RELEASE;
                keyEvent.timestamp = currentTime;
                osMessageQueuePut(KeyEventQueueHandle, &keyEvent, 0, 0);
            }
        }
        
        // 收集当前按下的按键信息
        if (guitarStatus.keyPressed[i]) {
            anyKeyPressed = 1;
            if (highestPriorityKey == 255) {
                highestPriorityKey = i; // 选择第一个按下的按键
            }
        }
    }
    
    // 检查按键组合并执行特殊功能
    Guitar_CheckKeyCombo();
    
    // 如果没有按键按下，停止音频输出
    if (!anyKeyPressed) {
        Guitar_StopNote();
    }
}

/**
 * @brief 检查按键组合并执行特殊功能
 */
void Guitar_CheckKeyCombo(void)
{
    // 统计当前按下的按键数量
    uint8_t pressedCount = 0;
    uint8_t firstPressed = 255;
    
    for (uint8_t i = 0; i < 6; i++) {
        if (guitarStatus.keyPressed[i]) {
            pressedCount++;
            if (firstPressed == 255) firstPressed = i;
        }
    }
    
    // Key1+Key2 = 播放C大调和弦
    if (guitarStatus.keyPressed[0] && guitarStatus.keyPressed[1] && pressedCount == 2) {
        Guitar_PlaySimpleChord(0);
    }
    // Key3+Key4 = 播放Am和弦  
    else if (guitarStatus.keyPressed[2] && guitarStatus.keyPressed[3] && pressedCount == 2) {
        Guitar_PlaySimpleChord(1);
    }
    // Key5+Key6 = 启动节拍器 (120 BPM, 8拍)
    else if (guitarStatus.keyPressed[4] && guitarStatus.keyPressed[5] && pressedCount == 2) {
        Guitar_SimpleMetronome(120, 8);
    }
    // 单个按键 = 正常音符演奏
    else if (pressedCount == 1 && firstPressed < 6) {
        uint16_t freq = Guitar_GetNoteFrequency(firstPressed);
        Guitar_UltraFastSwitchNote(firstPressed);
    }
    // 所有按键按下 = 颤音效果
    else if (pressedCount == 6) {
        Guitar_SimpleVibrato(0, 1); // 对C音符施加颤音
    }
}

/**
 * @brief 处理按键事件
 * @param keyEvent 按键事件指针
 */
void Guitar_ProcessKeyEvent(KeyEvent_t* keyEvent)
{
    if (!keyEvent || keyEvent->keyIndex >= 6) return;
    
    AudioCommand_t audioCmd;
    
    switch (keyEvent->eventType) {
        case KEY_EVENT_PRESS:
            // 按键按下 - 播放对应音符
            audioCmd.frequency = Guitar_GetNoteFrequency(keyEvent->keyIndex);
            audioCmd.duration = 0; // 持续播放直到释放
            audioCmd.volume = guitarStatus.volume;
            
            osMessageQueuePut(AudioCommandQueueHandle, &audioCmd, 0, 0);
            break;
            
        case KEY_EVENT_RELEASE:
            // 按键释放 - 停止播放
            audioCmd.frequency = 0;
            audioCmd.duration = 0;
            audioCmd.volume = 0;
            
            osMessageQueuePut(AudioCommandQueueHandle, &audioCmd, 0, 0);
            break;
            
        case KEY_EVENT_HOLD:
            // 长按处理 (暂时不实现)
            break;
    }
}

/**
 * @brief 测试OLED显示
 */
void Guitar_TestOLED(void)
{
    // 清屏
    OLED_Clear();
    
    // 测试不同位置的文字显示
    OLED_ShowString(0, 0, "OLED TEST", OLED_8X16);
    OLED_ShowString(0, 20, "Line 2", OLED_6X8);
    OLED_ShowString(0, 30, "Line 3", OLED_6X8);
    OLED_ShowString(0, 40, "Line 4", OLED_6X8);
    OLED_ShowString(0, 50, "Line 5", OLED_6X8);
    
    // 显示一些数字
    OLED_ShowNum(80, 20, 123, 3, OLED_6X8);
    OLED_ShowNum(80, 30, 456, 3, OLED_6X8);
    
    // 更新显示
    OLED_Update();
}

/**
 * @brief 更新OLED显示内容 (增强版)
 */
void Guitar_UpdateDisplay(void)
{
    char displayBuffer[32];
    
    // 清屏
    OLED_Clear();
    
    // === 第一行：标题和状态指示 ===
    OLED_ShowString(0, 0, "STM32 Guitar v2.0", OLED_6X8);
    
    // === 第二行：当前模式和八度 ===
    switch (guitarStatus.mode) {
        case GUITAR_MODE_SINGLE:
            sprintf(displayBuffer, "Mode:Single Oct:%d", guitarStatus.octave);
            break;
        case GUITAR_MODE_CHORD:
            sprintf(displayBuffer, "Mode:Chord  Oct:%d", guitarStatus.octave);
            break;
        case GUITAR_MODE_SCALE:
            sprintf(displayBuffer, "Mode:Scale  Oct:%d", guitarStatus.octave);
            break;
    }
    OLED_ShowString(0, 10, displayBuffer, OLED_6X8);
    
    // === 第三行：音量和当前音符 ===
    if (guitarStatus.currentNote > 0) {
        sprintf(displayBuffer, "Vol:%d%% Note:%dHz", guitarStatus.volume, guitarStatus.currentNote);
    } else {
        sprintf(displayBuffer, "Vol:%d%% Note:----", guitarStatus.volume);
    }
    OLED_ShowString(0, 20, displayBuffer, OLED_6X8);
    
    // === 第四行：按键状态指示 ===
    OLED_ShowString(0, 30, "Keys:", OLED_6X8);
    for (int i = 0; i < 6; i++) {
        if (guitarStatus.keyPressed[i]) {
            OLED_ShowString(30 + i * 8, 30, "*", OLED_6X8);  // 显示*表示按键按下
        } else {
            OLED_ShowString(30 + i * 8, 30, "o", OLED_6X8);  // 显示o表示按键释放
        }
    }
    
    // === 第五行：功能状态 ===
    strcpy(displayBuffer, "");
    // TODO: 添加录制和节拍器功能后再启用
    /*
    if (recorder.isRecording) {
        strcat(displayBuffer, "REC ");
    }
    if (recorder.isPlaying) {
        strcat(displayBuffer, "PLAY ");
    }
    if (metronome.isRunning) {
        char bpmStr[16];
        sprintf(bpmStr, "BPM:%d ", metronome.bpm);
        strcat(displayBuffer, bpmStr);
    }
    */
    
    if (strlen(displayBuffer) > 0) {
        OLED_ShowString(0, 40, displayBuffer, OLED_6X8);
    } else {
        OLED_ShowString(0, 40, "Ready to Rock!", OLED_6X8);
    }
    
    // === 第六行：音符名称显示 ===
    const char* noteNames[] = {"C", "D", "E", "F", "G", "A"};
    strcpy(displayBuffer, "Notes: ");
    for (int i = 0; i < 6; i++) {
        strcat(displayBuffer, noteNames[i]);
        if (i < 5) strcat(displayBuffer, " ");
    }
    OLED_ShowString(0, 50, displayBuffer, OLED_6X8);
    
    // 更新显示
    OLED_Update();
}

/**
 * @brief 快速无源蜂鸣器测试
 */
void Guitar_QuickBuzzerTest(void)
{
    // 播放Do Re Mi Fa So La音阶
    float notes[] = {261.63f, 293.66f, 329.63f, 349.23f, 392.00f, 440.00f};
    const char* noteNames[] = {"Do", "Re", "Mi", "Fa", "So", "La"};

    for (uint8_t i = 0; i < 6; i++) {
        // 使用优化的频率设置，固定50%占空比
        Guitar_SetPreciseFrequency(notes[i]);
        HAL_Delay(800);
        Guitar_StopNote();
        HAL_Delay(200);
    }
}
