#ifndef __GUITAR_DEBUG_H
#define __GUITAR_DEBUG_H

#include "guitar.h"

/**
 * @brief 电子吉他音频输出诊断工具
 * 
 * 这个模块提供了一系列诊断函数来检查PWM输出、
 * 频率计算和硬件连接问题。
 */

/* 诊断函数声明 */

/**
 * @brief 显示PWM诊断信息
 * 显示系统时钟、定时器时钟、PWM参数等信息
 */
void Guitar_ShowPWMDiagnostics(void);

/**
 * @brief 测试所有音符的PWM参数
 * 逐个测试每个音符的频率精度和PWM设置
 */
void Guitar_TestAllNotePWM(void);

/**
 * @brief 频率扫描测试
 * @param startFreq 起始频率
 * @param endFreq 结束频率  
 * @param stepTime 每个频率的持续时间(ms)
 */
void Guitar_FrequencySweep(uint16_t startFreq, uint16_t endFreq, uint16_t stepTime);

/**
 * @brief 检查硬件连接
 * 检查PWM输出引脚配置和定时器状态
 */
void Guitar_CheckHardwareConnection(void);

/**
 * @brief 播放测试音调序列
 * 播放标准的A3、A4、A5、A6音调用于测试
 */
void Guitar_PlayTestTones(void);

/**
 * @brief 显示音频输出电路建议
 * 显示推荐的音频输出电路连接方式
 */
void Guitar_ShowAudioCircuitAdvice(void);

/**
 * @brief 完整的音频系统诊断
 * 执行所有诊断测试的完整序列
 */
void Guitar_FullAudioDiagnostic(void);

/* 快速诊断宏 */
#define GUITAR_QUICK_PWM_CHECK()    Guitar_ShowPWMDiagnostics()
#define GUITAR_QUICK_TONE_TEST()    Guitar_PlayTestTones()

#endif /* __GUITAR_DEBUG_H */
