/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "guitar.h"
#include "OLED.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */

/* USER CODE END Variables */
/* Definitions for defaultTask */
osThreadId_t defaultTaskHandle;
const osThreadAttr_t defaultTask_attributes = {
  .name = "defaultTask",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityNormal,
};
/* Definitions for AudioTask */
osThreadId_t AudioTaskHandle;
const osThreadAttr_t AudioTask_attributes = {
  .name = "AudioTask",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityAboveNormal,
};
/* Definitions for DisplayTask */
osThreadId_t DisplayTaskHandle;
const osThreadAttr_t DisplayTask_attributes = {
  .name = "DisplayTask",
  .stack_size = 256 * 4,
  .priority = (osPriority_t) osPriorityNormal,
};
/* Definitions for KeyEventQueue */
osMessageQueueId_t KeyEventQueueHandle;
const osMessageQueueAttr_t KeyEventQueue_attributes = {
  .name = "KeyEventQueue"
};
/* Definitions for AudioCommandQueue */
osMessageQueueId_t AudioCommandQueueHandle;
const osMessageQueueAttr_t AudioCommandQueue_attributes = {
  .name = "AudioCommandQueue"
};
/* Definitions for I2C_Mutex */
osMutexId_t I2C_MutexHandle;
const osMutexAttr_t I2C_Mutex_attributes = {
  .name = "I2C_Mutex"
};

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */

/* USER CODE END FunctionPrototypes */

void StartKeyScanTask(void *argument);
void StartAudioTask(void *argument);
void StartDisplayTask(void *argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */
  /* Create the mutex(es) */
  /* creation of I2C_Mutex */
  I2C_MutexHandle = osMutexNew(&I2C_Mutex_attributes);

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  /* add semaphores, ... */
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* Create the queue(s) */
  /* creation of KeyEventQueue */
  KeyEventQueueHandle = osMessageQueueNew (10, 8, &KeyEventQueue_attributes);

  /* creation of AudioCommandQueue */
  AudioCommandQueueHandle = osMessageQueueNew (5, 12, &AudioCommandQueue_attributes);

  /* USER CODE BEGIN RTOS_QUEUES */
  /* add queues, ... */
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* creation of defaultTask */
  defaultTaskHandle = osThreadNew(StartKeyScanTask, NULL, &defaultTask_attributes);

  /* creation of AudioTask */
  AudioTaskHandle = osThreadNew(StartAudioTask, NULL, &AudioTask_attributes);

  /* creation of DisplayTask */
  DisplayTaskHandle = osThreadNew(StartDisplayTask, NULL, &DisplayTask_attributes);

  /* USER CODE BEGIN RTOS_THREADS */
  /* add threads, ... */
  /* USER CODE END RTOS_THREADS */

  /* USER CODE BEGIN RTOS_EVENTS */
  /* add events, ... */
  /* USER CODE END RTOS_EVENTS */

}

/* USER CODE BEGIN Header_StartKeyScanTask */
/**
  * @brief  Function implementing the defaultTask thread.
  * @param  argument: Not used
  * @retval None
  */
/* USER CODE END Header_StartKeyScanTask */
void StartKeyScanTask(void *argument)
{
  /* USER CODE BEGIN StartKeyScanTask */
  /* 按键扫描任务 */
  for(;;)
  {
    // 扫描所有按键
    Guitar_ScanKeys();
    
    // 任务延时10ms
    osDelay(10);
  }
  /* USER CODE END StartKeyScanTask */
}

/* USER CODE BEGIN Header_StartAudioTask */
/**
* @brief Function implementing the AudioTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartAudioTask */
void StartAudioTask(void *argument)
{
  /* USER CODE BEGIN StartAudioTask */
  /* 音频播放任务 */
  AudioCommand_t audioCmd;
  KeyEvent_t keyEvent;
  
  for(;;)
  {
    // 处理按键事件队列
    if (osMessageQueueGet(KeyEventQueueHandle, &keyEvent, NULL, 10) == osOK) {
      Guitar_ProcessKeyEvent(&keyEvent);
    }
    
    // 处理音频命令队列  
    if (osMessageQueueGet(AudioCommandQueueHandle, &audioCmd, NULL, 0) == osOK) {
      if (audioCmd.frequency > 0) {
        // 播放音符
        Guitar_SetFrequency(audioCmd.frequency);
        Guitar_SetVolume(audioCmd.volume);
      } else {
        // 停止播放
        Guitar_StopNote();
      }
    }
    
    // 检查音符持续时间
    if (guitarStatus.noteDuration > 0 && HAL_GetTick() >= guitarStatus.noteDuration) {
      Guitar_StopNote();
    }
    
    // 短延时
    osDelay(1);
  }
  /* USER CODE END StartAudioTask */
}

/* USER CODE BEGIN Header_StartDisplayTask */
/**
* @brief Function implementing the DisplayTask thread.
* @param argument: Not used
* @retval None
*/
/* USER CODE END Header_StartDisplayTask */
void StartDisplayTask(void *argument)
{
  /* USER CODE BEGIN StartDisplayTask */
  /* OLED显示任务 */
  
  // 等待其他任务启动
  osDelay(100);
  
  // LED指示初始化开始
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_RESET); // LED亮
  
  // 初始化OLED
  uint8_t oledInitRetry = 3;
  uint8_t oledInitResult = 1; // 1表示失败
  
  while (oledInitRetry > 0) {
    oledInitResult = OLED_PowerOnInit();
    if (oledInitResult == 0) {
      break; // 初始化成功
    }
    oledInitRetry--;
    
    // LED闪烁表示重试
    HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
    osDelay(500);
  }
  
  if (oledInitResult == 0) {
    // OLED初始化成功，LED熄灭
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_13, GPIO_PIN_SET);
    
    // 显示启动信息
    OLED_Clear();
    OLED_ShowString(0, 0, "STM32 Guitar", OLED_8X16);
    OLED_ShowString(0, 32, "Ready to Rock!", OLED_6X8);
    OLED_Update();
    osDelay(2000);
    
    // 正常显示循环
    for(;;)
    {
      // 获取I2C互斥锁
      if (osMutexAcquire(I2C_MutexHandle, 100) == osOK) {
        // 更新显示内容
        Guitar_UpdateDisplay();
        
        // 释放互斥锁
        osMutexRelease(I2C_MutexHandle);
      }
      
      // 每500ms更新一次显示
      osDelay(500);
    }
  } else {
    // OLED初始化失败，LED快速闪烁表示错误
    for(;;) {
      HAL_GPIO_TogglePin(GPIOC, GPIO_PIN_13);
      osDelay(200); // 快速闪烁
    }
  }
  /* USER CODE END StartDisplayTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

/* USER CODE END Application */

